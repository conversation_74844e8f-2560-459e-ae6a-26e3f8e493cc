const Keyv = require('keyv').default;
const config = require('./config'); // centralized config with caching settings
const logger = require('./logger'); // your logger instance

let keyv;

switch (config.caching.driver.toLowerCase()) {
  case 'redis': {
    const redisUrl = `redis://${config.caching.redis.host}:${config.caching.redis.port}`;
    keyv = new Keyv(redisUrl, { namespace: 'cache', ttl: config.caching.ttl });
    keyv.on('error', err => logger.error('Keyv Redis connection error:', err));
    logger.info(`Using Redis for caching on: (${redisUrl})`);
    break;
  }
  case 'memcache': {
    // For memcache, the connection URL should be in memcache:// format.
    const memcacheUrl = `memcache://${config.caching.memcached.host}`;
    keyv = new Keyv(memcacheUrl, { namespace: 'cache', ttl: config.caching.ttl });
    keyv.on('error', err => logger.error('Keyv Memcache connection error:', err));
    logger.info(`Using Memcache for caching on: (${memcacheUrl})`);
    break;
  }
  default: {
    // In-memory store
    keyv = new Keyv({ namespace: 'cache', ttl: config.caching.ttl });
    keyv.on('error', err => logger.error('Keyv in-memory connection error:', err));
    logger.info('Using in-memory caching');
    break;
  }
}

module.exports = keyv;