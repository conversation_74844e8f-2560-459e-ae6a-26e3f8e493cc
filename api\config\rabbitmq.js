const config = require("./config.js");
const logger = require("./logger.js");

async function connectRabbitmq() {
  try {
    // Dynamically import amqplib as it's an ES module
    const amqp = await import("amqplib");
    const connection = await amqp.default.connect(config.messageQueuing.rabitmq_url);
    const channel = await connection.createChannel();
    if (connection && channel) {
      logger.info("Successfully connected to RabbitMQ and channel is ready");
    }
    global.channel = channel;
    return { connection, channel };
  } catch (error) {
    logger.error("Error connecting to RabbitMQ:", error);
    process.exit(1); // exit if we can't connect (or handle as needed)
  }
}

module.exports = connectRabbitmq;
