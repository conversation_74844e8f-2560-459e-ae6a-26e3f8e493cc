const httpStatus = require("http-status");
const models = require("../models");
const { Badge } = models;
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { paginate } = require("../models/plugins/paginate.plugin");
const { Op } = require("sequelize");
const { generateBadgeImage } = require("../services/badge.service");

/**
 * Get all badges with pagination and search functionality.
 *
 * @async
 * @function index
 * @param {Object} req - Express request object. Accepts query parameters for pagination and search.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a paginated response with badge data.
 */
exports.index = catchAsync(async (req, res) => {
  const { page, limit, sortBy, sortOrder, search } = req.query;
  const paginationOptions = { page, limit, sortBy, sortOrder };

  const queryOptions = {
    order: [[sortBy, sortOrder.toUpperCase() === "asc" ? "asc" : "desc"]],
    attributes: ["badge_id", "name", "status"],
  };

  if (search) {
    queryOptions.where = {
      [Op.or]: [
        { name: { [Op.iLike]: `%${search}%` } },
      ],
    };
  }

  const result = await paginate(Badge, queryOptions, paginationOptions);

  sendSuccess(res, "Badges retrieved successfully", httpStatus.OK, result);
});

/**
 * Get a single badge by its ID with all details.
 *
 * @async
 * @function show
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains badgeId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the badge details or a 404 error if not found.
 */
exports.show = catchAsync(async (req, res) => {
  const { badgeId } = req.params;
  
  const badge = await Badge.findByPk(badgeId);

  if (!badge) {
    return sendError(res, "Badge not found", httpStatus.NOT_FOUND);
  }

  sendSuccess(res, "Badge retrieved successfully", httpStatus.OK, badge);
});

/**
 * Create a new badge.
 *
 * @async
 * @function create
 * @param {Object} req - Express request object.
 * @param {Object} req.body - Badge data.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the created badge or an error response.
 */
exports.create = catchAsync(async (req, res) => {
  const { name, status, images, content, schema, key, format, updated_by } = req.body;

  const badge = await Badge.create({
    name,
    status,
    images,
    content,
    schema,
    key,
    format,
    updated_by,
  });

  sendSuccess(res, "Badge created successfully", httpStatus.CREATED, badge);
});

/**
 * Update an existing badge.
 *
 * @async
 * @function update
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains badgeId.
 * @param {Object} req.body - Updated badge data.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the updated badge or an error response.
 */
exports.update = catchAsync(async (req, res) => {
  const { badgeId } = req.params;
  const updateData = req.body;

  const badge = await Badge.findByPk(badgeId);

  if (!badge) {
    return sendError(res, "Badge not found", httpStatus.NOT_FOUND);
  }

  await badge.update(updateData);

  sendSuccess(res, "Badge updated successfully", httpStatus.OK, badge);
});

/**
 * Delete a badge.
 *
 * @async
 * @function delete
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains badgeId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success message or an error response.
 */
exports.delete = catchAsync(async (req, res) => {
  const { badgeId } = req.params;

  const badge = await Badge.findByPk(badgeId);

  if (!badge) {
    return sendError(res, "Badge not found", httpStatus.NOT_FOUND);
  }

  await badge.destroy();

  sendSuccess(res, "Badge deleted successfully", httpStatus.OK, {});
});

/**
 * Get all available schema names from models directory.
 *
 * @async
 * @function getSchemas
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends list of available schema names.
 */
exports.getSchemas = catchAsync(async (req, res) => {
  const schemas = Object.keys(models).filter(key =>
    key !== 'sequelize' && key !== 'Sequelize'
  );

  sendSuccess(res, "Schema names retrieved successfully", httpStatus.OK, { schemas });
});

/**
 * Get model attributes for a specific model.
 *
 * @async
 * @function getModelAttributes
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains modelName.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends model attributes or error if model not found.
 */
exports.getModelAttributes = catchAsync(async (req, res) => {
  const { modelName } = req.params;

  // Convert modelName to proper case (e.g., "patient" -> "Patient")
  const modelKey = modelName.charAt(0).toUpperCase() + modelName.slice(1);

  if (!models[modelKey]) {
    return sendError(res, "Model not found", httpStatus.NOT_FOUND);
  }

  const model = models[modelKey];
  const attributes = Object.keys(model.rawAttributes).map(attr => ({
    name: attr,
    type: model.rawAttributes[attr].type.toString(),
    allowNull: model.rawAttributes[attr].allowNull,
    primaryKey: model.rawAttributes[attr].primaryKey || false,
  }));

  sendSuccess(res, "Model attributes retrieved successfully", httpStatus.OK, {
    modelName: modelKey,
    attributes
  });
});

/**
 * Print badge by generating image from template and instance data.
 *
 * @async
 * @function printBadge
 * @param {Object} req - Express request object.
 * @param {Object} req.body - Contains badge_id and instance_id.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends generated badge image or error.
 */
exports.printBadge = catchAsync(async (req, res) => {
  const { badge_id, instance_id } = req.body;

  // Get badge template
  const badge = await Badge.findByPk(badge_id);
  if (!badge) {
    return sendError(res, "Badge not found", httpStatus.NOT_FOUND);
  }

  // Validate content structure
  if (!badge.content || !badge.content.canvasConfig || !badge.content.elements) {
    return sendError(res, "Invalid badge template content", httpStatus.BAD_REQUEST);
  }

  // Get the model based on schema
  const modelKey = badge.schema.charAt(0).toUpperCase() + badge.schema.slice(1);
  if (!models[modelKey]) {
    return sendError(res, "Schema model not found", httpStatus.NOT_FOUND);
  }

  // Query the instance using the key column
  const whereClause = {};
  whereClause[badge.key] = instance_id;

  const instance = await models[modelKey].findOne({ where: whereClause });
  if (!instance) {
    return sendError(res, "Instance not found", httpStatus.NOT_FOUND);
  }

  // Map variables to instance data
  const mappedData = {};
  if (badge.variables && Array.isArray(badge.variables)) {
    badge.variables.forEach(variable => {
      if (instance[variable]) {
        mappedData[variable] = instance[variable];
      }
    });
  }

  try {
    // Generate image using canvas with template configuration
    const imageBuffer = await generateBadgeImage(badge.content, mappedData, badge.format);

    // Set appropriate headers
    const format = badge.format.toLowerCase();
    res.set({
      'Content-Type': `image/${format === 'pdf' ? 'png' : format}`,
      'Content-Length': imageBuffer.length,
      'Content-Disposition': `attachment; filename="badge_${instance_id}.${format === 'pdf' ? 'png' : format}"`
    });

    res.send(imageBuffer);
  } catch (error) {
    console.error('Badge generation error:', error);
    return sendError(res, "Failed to generate badge image", httpStatus.INTERNAL_SERVER_ERROR);
  }
});
