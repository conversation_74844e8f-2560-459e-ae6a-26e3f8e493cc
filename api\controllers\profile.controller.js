const { LanguagePreference, Identity, Facility, Address, Country, State, MasterData, Language } = require("../models");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { status: httpStatus } = require('http-status');

/**
 * @desc    Update user language preference
 * @param   {Object} req - Express request object
 * @param   {Object} req.body - Contains the language_id to set as preference
 * @param   {Object} req.user - Contains the current user's identity_id
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object confirming the update
 */

exports.language = catchAsync(async (req, res) => {
  const { language_id, updated_by } = req.body;
  const { identity_id } = req.identity;

  // Update or create the language preference for the user
  await LanguagePreference.upsert({
    identity_id,
    language_id,
    updated_by,
  });

  sendSuccess(res, "Language preference updated successfully.", httpStatus.OK);
});

/**
 * @desc    Get current user profile details
 * @param   {Object} req - Express request object
 * @param   {Object} req.identity - Contains the current user's identity_id from JWT token
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object with user profile information
 */
exports.getCurrentUserProfile = catchAsync(async (req, res) => {
  const { identity_id } = req.identity;

  if (!identity_id) {
    return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
  }

  // Fetch user profile with related data
  const userProfile = await Identity.findByPk(identity_id, {
    attributes: [
      'identity_id',
      'email',
      'first_name',
      'last_name',
      'middle_name',
      'eid',
      'identity_type',
      'national_id',
      'mobile',
      'start_date',
      'end_date',
      'status',
      'suspension',
      'suspension_date',
      'reason',
      'image',
      'company',
      'organization',
      'company_code',
      'job_title',
      'job_code',
      'manager',
      'created_at',
      'updated_at'
    ],
    include: [
      {
        model: Facility,
        as: 'facility',
        attributes: ['facility_id', 'name'],
        include: [
          {
            model: Address,
            as: 'address',
            attributes: [
              'address_line_1',
              'address_line_2',
              'postal_code',
              'region'
            ],
            include: [
              {
                model: Country,
                as: 'country',
                attributes: ['name']
              },
              {
                model: State,
                as: 'state',
                attributes: ['name']
              }
            ]
          }
        ]
      },
      {
        model: MasterData,
        as: 'identity_status_name',
        attributes: ['value']
      },
      {
        model: MasterData,
        as: 'identity_type_name',
        attributes: ['value']
      }
    ]
  });

  if (!userProfile) {
    return sendError(res, "User profile not found", httpStatus.NOT_FOUND);
  }

  // Get language preference
  const languagePreference = await LanguagePreference.findOne({
    where: { identity_id },
    include: [
      {
        model: Language,
        as: 'language',
        attributes: ['language_id', 'name', 'code']
      }
    ]
  });

  // Format response data
  const profileData = {
    identity_id: userProfile.identity_id,
    email: userProfile.email,
    first_name: userProfile.first_name,
    last_name: userProfile.last_name,
    middle_name: userProfile.middle_name,
    eid: userProfile.eid,
    identity_type: userProfile.identity_type,
    identity_type_name: userProfile.identity_type_name?.value || null,
    national_id: userProfile.national_id,
    mobile: userProfile.mobile,
    start_date: userProfile.start_date,
    end_date: userProfile.end_date,
    status: userProfile.status,
    status_name: userProfile.identity_status_name?.value || null,
    suspension: userProfile.suspension,
    suspension_date: userProfile.suspension_date,
    reason: userProfile.reason,
    image: userProfile.image,
    company: userProfile.company,
    organization: userProfile.organization,
    company_code: userProfile.company_code,
    job_title: userProfile.job_title,
    job_code: userProfile.job_code,
    manager: userProfile.manager,
    facility: userProfile.facility ? {
      facility_id: userProfile.facility.facility_id,
      name: userProfile.facility.name,
      address: userProfile.facility.address ? {
        address_line_1: userProfile.facility.address.address_line_1,
        address_line_2: userProfile.facility.address.address_line_2,
        postal_code: userProfile.facility.address.postal_code,
        region: userProfile.facility.address.region,
        country: userProfile.facility.address.country?.name || null,
        state: userProfile.facility.address.state?.name || null
      } : null
    } : null,
    language_preference: languagePreference ? {
      language_id: languagePreference.language?.language_id || null,
      name: languagePreference.language?.name || null,
      code: languagePreference.language?.code || null
    } : null,
    created_at: userProfile.created_at,
    updated_at: userProfile.updated_at
  };

  sendSuccess(res, "User profile retrieved successfully", httpStatus.OK, profileData);
});