const { getCachedMasterDataValue } = require("./caching.helper");
const models = require("../models");

module.exports = function (parseHL7Date) {
    return {
        applyHL7DateParsing: function (obj) {
            const dateKeywords = ['date', 'dob', 'timestamp'];

            for (const sectionKey in obj) {
                if (!obj.hasOwnProperty(sectionKey)) continue;

                const section = obj[sectionKey];
                if (typeof section !== 'object' || section === null) continue;

                for (const fieldKey in section) {
                    if (!section.hasOwnProperty(fieldKey)) continue;

                    const value = section[fieldKey];

                    if (
                        typeof value === 'string' &&
                        value != '' &&
                        dateKeywords.some(keyword => fieldKey.toLowerCase().includes(keyword))
                        && !value.includes('-') && !value.includes('/')
                    ) {
                        section[fieldKey] = parseHL7Date(value);
                    }
                }
            }

            return obj;
        },

        resolveMasterDataFields: async function (transformed, groupMap) {
            for (const [modelName, fields] of Object.entries(groupMap)) {
                if (!transformed[modelName]) continue;

                for (const [field, group] of Object.entries(fields)) {
                    const raw = transformed[modelName][field];

                    if (raw !== undefined && raw !== null) {
                        const key = await getCachedMasterDataValue(group, raw);
                        if (key !== null) {
                            transformed[modelName][field] = key;
                        } else {
                            // fallback to model default (if any)
                            const attr = models[modelName]?.rawAttributes[field];
                            if (attr) {
                                transformed[modelName][field] =
                                    attr.defaultValue ?? attr.default ?? raw;
                            }
                        }
                    }
                }
            }

            return transformed;
        },

        cleanDTLResult: cleanDTLResult
    }
}

function cleanDTLResult(obj, typeOfFunction) {

    const result = {};
    for (const [key, value] of Object.entries(obj)) {
        if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
            const nested = cleanDTLResult(value, typeOfFunction);
            if (Object.keys(nested).length > 0) result[key] = nested;
        } else if (isValidKey(value)) {
            result[key] = value;
        }
    }
    return result;


    function isValidKey(value) {
        let conditionToDisRegardKey = ![null, undefined, ''].includes(value);
        if (typeOfFunction == 'parseData')
            conditionToDisRegardKey = ![null, undefined].includes(value);
        return conditionToDisRegardKey;
    }
}