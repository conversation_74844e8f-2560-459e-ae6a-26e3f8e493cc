'use strict';
module.exports = {
  up: async (queryInterface, Sequelize) => {
    // This will create the fuzzystrmatch extension
    await queryInterface.sequelize.query('CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;');
  },
  down: async (queryInterface, Sequelize) => {
    // This will drop the fuzzystrmatch extension (if needed)
    await queryInterface.sequelize.query('DROP EXTENSION IF EXISTS fuzzystrmatch;');
  }
};