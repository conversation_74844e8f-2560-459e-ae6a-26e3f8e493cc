"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(`
      CREATE VIEW view_patient_appointment_guest AS
      SELECT
        ag.appointment_guest_id                       AS appointment_guest_id,
        ag.facility_id                                AS facility_id,
        pg.patient_guest_id                           AS patient_guest_id,
        pg.first_name                                 AS first_name,
        pg.last_name                                  AS last_name,
        CONCAT(pg.first_name, ' ', pg.last_name)      AS guest_full_name,
        pg.email                                      AS email,
        pg.phone                                      AS phone,
        pg.friends_and_family                         AS friends_and_family,
        pg.is_walkin                                  AS is_walkin,
        pgm.value                                     AS guest_image,
        pg.guest_type                                 AS guest_type,
        pi.identifier_value                           AS mrn,
        ag.arrival_time                               AS guest_arrival_time,
        ag.departure_time                             AS guest_departure_time,
        ag.guest_pin                                  AS guest_pin,
  
        -- current appointment status
        ag.status                                     AS appointment_guest_status,
        ms_status.value                               AS appointment_guest_status_name,
  
        -- most‐recent status across ALL appointments
        last_ag.last_status                           AS last_appointment_guest_status,
        ms_last.value                                 AS last_appointment_guest_status_name,
  
        a.appointment_date                            AS appointment_date,
        a.appointment_id                              AS appointment_id,
        a.patient_id                                  AS patient_id,
        CONCAT(p.first_name, ' ', p.last_name)        AS patient_full_name,
        ag.screening                                  AS screening,
  
        f.name                                        AS facility_name,
        fl.floor_number                               AS floor_number,
        b.name                                        AS building_name,
        r.room_number                                 AS room_number,
        a.provider_name                               AS provider_name
  
      FROM appointment_guest ag
      JOIN patient_guest pg
        ON ag.patient_guest_id = pg.patient_guest_id
  
      /* grab the guest's most recent status ever */
      LEFT JOIN LATERAL (
        SELECT ag2.status AS last_status
        FROM appointment_guest ag2
        WHERE ag2.patient_guest_id = pg.patient_guest_id
        ORDER BY ag2.arrival_time DESC NULLS LAST
        LIMIT 1
      ) last_ag ON true
  
      /* join to get readable names */
      LEFT JOIN master_data ms_status
        ON ms_status.key   = ag.status
       AND ms_status.group = 'appointment_guest_status'
  
      LEFT JOIN master_data ms_last
        ON ms_last.key     = last_ag.last_status
       AND ms_last.group   = 'appointment_guest_status'
  
      /* rest of your original joins */
      LEFT JOIN patient_identifier pi
        ON pi.patient_id = pg.patient_id
  
      LEFT JOIN appointment a
        ON ag.appointment_id = a.appointment_id
  
      LEFT JOIN patient_guest_media pgm
        ON pg.image = pgm.patient_guest_media_id
  
      LEFT JOIN patient p
        ON a.patient_id = p.patient_id
  
      LEFT JOIN facility f
        ON ag.facility_id = f.facility_id
  
      LEFT JOIN floor fl
        ON fl.facility_id = a.facility_id
  
      LEFT JOIN building b
        ON b.facility_id = a.facility_id
  
      LEFT JOIN room r
        ON r.facility_id = a.facility_id;
    `);
  },

  down: async (queryInterface, Sequelize) => {
    // Remove the view on rollback
    return queryInterface.sequelize.query(
      `DROP VIEW IF EXISTS view_patient_appointment_guest;`
    );
  },
};
