'use strict';

module.exports = {
    up: async (queryInterface, Sequelize) => {
        return queryInterface.sequelize.query(`
    CREATE VIEW view_visit_extended AS
       SELECT
        v.visit_id,
        v.title,
        v.type,
        v.category,
        v.start_date,
        v.start_time,
        v.duration,
        v.end_date,
        v.repeat_visit,
        v.facility_id,
        v.access_level_id,
        v.host_id,
        v.check_in_instruction,
        v.escort_id,
        v.send_notification,
        v.remind_me,
        v.visitor_message,
        v.status,
        v.updated_by,
        v.created_at,
        v.updated_at,
        f.name AS facility_name,
        i.first_name AS host_first_name,
        i.last_name AS host_last_name,
        (i.first_name || ' ' || i.last_name) AS host_name,
        i.email AS host_email,
        i.mobile AS host_phone,
       COALESCE(
  jsonb_agg(
    CASE 
      WHEN gv.guest_visit_id IS NOT NULL THEN
        jsonb_build_object(
          'guest_visit_id', gv.guest_visit_id,
          'guest_id', g.guest_id,
          'guest_first_name', g.first_name,
          'guest_last_name', g.last_name,
          'guest_email', g.email,
          'guest_image', g.image,
          'check_in_time', gv.check_in_time,
          'check_out_time', gv.check_out_time,
          'guest_status', gv.guest_status,
          'guest_pin', gv.guest_pin,
          'screening', gv.screening,
          'created_at', gv.created_at
        )
      ELSE NULL
    END
  ) FILTER (WHERE gv.guest_visit_id IS NOT NULL),
  '[]'::jsonb
) AS extra_data
      FROM visit v
      LEFT JOIN facility f ON v.facility_id = f.facility_id
      LEFT JOIN identity i ON v.host_id = i.identity_id
      LEFT JOIN guest_visit gv ON v.visit_id = gv.visit_id
      LEFT JOIN guest g ON gv.guest_id = g.guest_id
      GROUP BY 
        v.visit_id, v.title, v.type, v.category, v.start_date,
        v.start_time, v.duration, v.end_date, v.repeat_visit,
        v.facility_id, v.access_level_id, v.host_id, v.check_in_instruction,
        v.escort_id, v.send_notification, v.remind_me, v.visitor_message,
        v.status, v.updated_by, v.created_at, v.updated_at,
        f.name, i.first_name, i.last_name, i.email, i.mobile;
    `);
    },

    down: async (queryInterface, Sequelize) => {
        return queryInterface.sequelize.query(
            `DROP VIEW IF EXISTS view_visit_extended;`
        );
    },
};