const history = require("../models/plugins/history.plugin");
const trigger = require("./plugins/trigger.plugin");
const rules = require("./rules/appointment.rule.json");

module.exports = (sequelize, DataTypes) => {
  const Appointment = sequelize.define(
    "Appointment",
    {
      appointment_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      hl7_appointment_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      patient_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "patient",
          key: "patient_id",
        },
        onDelete: "CASCADE",
      },
      function_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "function",
          key: "function_id",
        },
        onDelete: "CASCADE",
      },
      appointment_date: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      department: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      provider_name: {
        type: DataTypes.STRING(200),
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      type: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      facility_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "facility",
          key: "facility_id",
        },
        onDelete: "CASCADE",
      },

      room: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      beds: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      arrival_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      departure_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      screening: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      location: {
        type: DataTypes.STRING(100),
        allowNull: true,
      }
    },
    {
      tableName: "appointment",
      timestamps: true,
      underscored: true,
    }
  );

  Appointment.associate = (models) => {

    Appointment.belongsTo(models.Function, {
      foreignKey: "function_id",
      as: "function",
    });
    Appointment.belongsTo(models.Patient, {
      foreignKey: "patient_id",
      as: "patient",
    });
    Appointment.belongsTo(models.Facility, {
      foreignKey: "facility_id",
      as: "facility",
    });

    Appointment.belongsTo(models.MasterData, {
      foreignKey: "type",
      targetKey: "key",
      as: "appointment_type_name",
      constraints: false,
      scope: {
        group: "appointment_type",
      },
    });

    Appointment.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "appointment_status_name",
      constraints: false,
      scope: {
        group: "appointment_status",
      },
    });
  };

  history(Appointment, sequelize, DataTypes);
  trigger(Appointment, ["create","update"], rules);
    
  return Appointment;
};
