const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const AppointmentGuestCheckin = sequelize.define(
    "AppointmentGuestCheckin",
    {
      appointment_guest_checkin_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      appointment_guest_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "appointment_guest",
          key: "appointment_guest_id",
        },
        onDelete: "CASCADE",
      },
      checkin_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      checkout_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      tableName: "appointment_guest_checkin",
      timestamps: true,
      underscored: true,
    }
  );

  AppointmentGuestCheckin.associate = (models) => {
    AppointmentGuestCheckin.belongsTo(models.AppointmentGuest, {
      foreignKey: "appointment_guest_id",
      as: "appointmentGuest",
      onDelete: "CASCADE",
    });
  };

  history(AppointmentGuestCheckin, sequelize, DataTypes);

  return AppointmentGuestCheckin;
};
