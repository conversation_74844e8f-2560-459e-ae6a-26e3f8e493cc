const history = require('../models/plugins/history.plugin');

module.exports = (sequelize, DataTypes) => {
  const CronConfig = sequelize.define(
    'CronConfig',
    {
      cron_config_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true
      },
      display_name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true
      },
      description: {
        type: DataTypes.STRING,
        allowNull: true
      },
      schedule: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          is: /^\s*([0-5]?\d|\*)\s+([0-5]?\d|\*)\s+([01]?\d|2[0-3]|\*)\s+([1-9]|[12]\d|3[01]|\*)\s+([1-9]|1[0-2]|\*)\s+([0-6]|\*)\s*$/,
        },
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: 'cron_config',
      timestamps: true,
      underscored: true,
    }
  );

  // Apply the history plugin to the model
  history(CronConfig, sequelize, DataTypes);

  return CronConfig;
};
