const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
    const FacilityAccessLevel = sequelize.define(
      "FacilityAccessLevel",
      {
        facility_access_level_id: {
          type: DataTypes.UUID,
          primaryKey: true,
          defaultValue: DataTypes.UUIDV4,
        },
        access_level_id: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        facility_id: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: "facility",
            key: "facility_id",
          },
        },
        building_id: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        floor_id: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        room_id: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        entry_restrictions: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        access_protocol: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        requestable_guest:{
          type:DataTypes.BOOLEAN,
          allowNull:true,
        },
        default_access_guest:{
          type:DataTypes.BOOLEAN,
          allowNull:true,
        },
        default_access_identity:{
          type:DataTypes.BOOLEAN,
          allowNull:true,
        },
        identity_type:{
          type:DataTypes.ARRAY(DataTypes.ENUM("COS" , "EMP")),
          allowNull:true,
        },
        updated_by: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      },
      {
        tableName: "facility_access_level",
        timestamps: true,
        underscored: true,
      }
    );
  
    FacilityAccessLevel.associate = (models) => {
      // Each FacilityAccessLevel belongs to an AccessLevel
      FacilityAccessLevel.belongsTo(models.AccessLevel, {
        foreignKey: "access_level_id",
        as: "access_level",
      });
      FacilityAccessLevel.belongsTo(models.Facility, {
        foreignKey: "facility_id",
        as: "facility",
      });
      FacilityAccessLevel.belongsTo(models.Building, {
        foreignKey: "building_id",
        as: "building",
      });
      FacilityAccessLevel.belongsTo(models.Floor, {
        foreignKey: "floor_id",
        as: "floor",
      });
      FacilityAccessLevel.belongsTo(models.Room, {
        foreignKey: "room_id",
        as: "room",
      });
    };

    history(FacilityAccessLevel, sequelize, DataTypes);
  
    return FacilityAccessLevel;
  };
  