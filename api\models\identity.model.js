const { Op } = require('sequelize');
const history = require("../models/plugins/history.plugin");
const trigger = require("../models/plugins/trigger.plugin");
const identityRules = require("./rules/identity.rules.json");

module.exports = (sequelize, DataTypes) => {
  const Identity = sequelize.define(
    'Identity',
    {
      identity_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      facility_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "facility",
          key: "facility_id",
        },
        onDelete: "CASCADE",
      },
      // username: {
      //   type: DataTypes.STRING(50),
      //   allowNull: false,
      //   unique: true,
      //   set(value) {
      //     if (typeof value === "string") this.setDataValue('username', value.trim());
      //   },
      // },
      email: {
        type: DataTypes.STRING(255),
        allowNull: false,
        unique: true,
        set(value) {
          if (typeof value === "string") this.setDataValue('email', value.toLowerCase().trim());
        },
      },
      first_name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      last_name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      middle_name: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      eid: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      identity_type: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      national_id: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },

      suffix: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      mobile: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      start_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      end_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      suspension: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      suspension_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      image: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      company: {
        type: DataTypes.STRING(100),
        allowNull: true
      },
      // language:{
      //  type: DataTypes.ENUM('en', 'es'),
      //  allowNull:false,
      //  defaultValue: "en"
      // },
      organization: {
        type: DataTypes.STRING(100),
        allowNull: true
      },
      company_code: {
        type: DataTypes.STRING(100),
        allowNull: true
      },
      job_title: {
        type: DataTypes.STRING(100),
        allowNull: true
      },
      job_code: {
        type: DataTypes.STRING(100),
        allowNull: true
      },
      manager: {
        type: DataTypes.UUID,
        allowNull: true
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: 'identity',
      timestamps: true,
      underscored: true,
    }
  );

  /**
   * Static Method: Check if an email is already taken.
   * @param {string} email - The email to check.
   * @param {string} [excludeIdentityId] - Optional identity_id to exclude.
   * @returns {Promise<boolean>}
   */
  Identity.isEmailTaken = async function (email, excludeIdentityId) {
    const where = { email: typeof value === "string" ? email.toLowerCase().trim() : email.toLowerCase() };
    if (excludeIdentityId) {
      where.identity_id = { [Op.ne]: excludeIdentityId };
    }
    const identity = await Identity.findOne({ where });
    return !!identity;
  };

  /**
   * Static Method: Check if a username is already taken.
   * @param {string} username - The username to check.
   * @param {string} [excludeIdentityId] - Optional identity_id to exclude.
   * @returns {Promise<boolean>}
   */
  // Identity.isUsernameTaken = async function (username, excludeIdentityId) {
  //   const where = { username: typeof value === "string" ? username.trim() : username };
  //   if (excludeIdentityId) {
  //     where.identity_id = { [Op.ne]: excludeIdentityId };
  //   }
  //   const identity = await Identity.findOne({ where });
  //   return !!identity;
  // };

  Identity.associate = (models) => {
    Identity.belongsTo(models.Facility, {
      foreignKey: "facility_id",
      as: "facility",
    });
    // One-to-one: Each Identity has one IdentityVerification (password info)
    Identity.hasOne(models.IdentityVerification, {
      foreignKey: 'identity_id',
      as: 'identity_verification',
      onDelete: 'CASCADE',
    });
    // Many-to-many: Identity <-> Role via IdentityRole mapping table
    Identity.belongsToMany(models.Role, {
      through: models.IdentityRole,
      foreignKey: 'identity_id',
      otherKey: 'role_id',
      as: 'role',
    });
    Identity.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "identity_status_name",
      constraints: false,
      scope: {
        group: "identity_status",
      },
    });
    Identity.belongsTo(models.MasterData, {
      foreignKey: "identity_type",
      targetKey: "key",
      as: "identity_type_name",
      constraints: false,
      scope: {
        group: "identity_type",
      },
    });
  };

  history(Identity, sequelize, DataTypes);

  // Add trigger plugin for identity events
  trigger(Identity, ["create", "update"], identityRules);

  return Identity;
};
