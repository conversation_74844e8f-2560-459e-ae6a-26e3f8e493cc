const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const LanguagePreference = sequelize.define(
    "LanguagePreference",
    {
      language_preference_id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      identity_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "identity",
          key: "identity_id",
        },
        unique: true,
      },
      language_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "language",         // references the table name "languages"
          key: "language_id",
        },
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "language_preference",
      timestamps: false,
    }
  );

  // History plugin for audit trails
  history(LanguagePreference, sequelize, DataTypes);

  // Associations
  LanguagePreference.associate = (models) => {
    // Each preference belongs to one language
    LanguagePreference.belongsTo(models.Language, {
      foreignKey: "language_id",
      as: "language",
    });

    // Each preference belongs to one identity
    LanguagePreference.belongsTo(models.Identity, {
      foreignKey: "identity_id",
      as: "identity",
    });
  };

  return LanguagePreference;
};