const { DataTypes } = require("sequelize");


module.exports = (sequalize, DataTypes) => {
    const NotificationNovu = sequalize.define(
        "NotificationNovu",
        {
            notification_novu_id: {
                type: DataTypes.UUID,
                primaryKey: true,
                defaultValue: DataTypes.UUIDV4,
            },
            notification_id: {
                type: DataTypes.UUID,
                allowNull: false,
            },
            workflow_id: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            subscriber_id: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            receiver_first_name: {
                type: DataTypes.STRING,
                allowNull: true,
            },
            receiver_last_name: {
                type: DataTypes.STRING,
                allowNull: true,
            },
            receiver_email: {
                type: DataTypes.STRING,
                allowNull: true,
            },
            receiver_phone: {
                type: DataTypes.STRING,
                allowNull: true,
            }
        } ,
        {
            tableName: "notification_novu",
            timestamps: true,
            underscored: true,
        }
    );

    NotificationNovu.associate = (models) => {
        NotificationNovu.belongsTo(models.Notification, {
            foreignKey: "notification_id",
            as: "notification",
        });
    };

    return NotificationNovu;
}