const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const PatientIdentifier = sequelize.define(
    "PatientIdentifier",
    {
      patient_identifier_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      patient_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      identifier_type: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      identifier_value: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      assigning_authority: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      effective_from: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      effective_to: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
    },
    {
      tableName: "patient_identifier",
      timestamps: true,
      underscored: true,
    }
  );

  PatientIdentifier.associate = (models) => {
    PatientIdentifier.belongsTo(models.Patient, {
      foreignKey: "patient_id",
      as: "patient",
    });

    PatientIdentifier.belongsTo(models.MasterData, {
      foreignKey: "identifier_type",
      targetKey: "key",
      as: "patient_identifier_identifier_type_name",
      constraints: false,
      scope: {
        group: "patient_identifier_identifier_type",
      },
    });
    PatientIdentifier.belongsTo(models.MasterData, {
      foreignKey: "assigning_authority",
      targetKey: "key",
      as: "patient_identifier_assigning_authority_name",
      constraints: false,
      scope: {
        group: "patient_identifier_assigning_authority",
      },
    });
  };

  history(PatientIdentifier, sequelize, DataTypes);

  return PatientIdentifier;
};
