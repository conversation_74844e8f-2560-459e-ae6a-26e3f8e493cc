const { MEDIA } = require("../config/attributes");
const history = require("./plugins/history.plugin");
const media = require("./plugins/media.plugin");

module.exports = (sequelize, DataTypes) => {
  const PatientNdaSignature = sequelize.define(
    "PatientNdaSignature",
    {
      patient_nda_signature_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      patient_nda_agreement_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "nda_agreement",
          key: "patient_nda_agreement_id",
        },
        onDelete: "CASCADE",
      },
      signature_method: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      signature: {
        type: MEDIA,
        allowNull: true,
        allowMultiple: false,
      },
    },
    {
      tableName: "nda_signature",
      timestamps: true,
      underscored: true,
    }
  );

  PatientNdaSignature.associate = (models) => {
    PatientNdaSignature.belongsTo(models.PatientNdaAgreement, {
      foreignKey: "patient_nda_agreement_id",
      as: "agreement",
    });

    PatientNdaSignature.belongsTo(models.MasterData, {
      foreignKey: "signature_method",
      targetKey: "key",
      as: "patient_nda_signature_method_name",
      constraints: false,
      scope: {
        group: "patient_nda_signature_method",
      },
    });
  };

  history(PatientNdaSignature, sequelize, DataTypes);
  media(PatientNdaSignature, sequelize, DataTypes);


  return PatientNdaSignature;
};
