[{"name": "Identity Modified", "conditions": {"all": [{"fact": "function", "operator": "equal", "value": "parseData"}, {"fact": "email", "operator": "notEqual", "value": null}]}, "event": [{"type": "notification", "params": {"notification": "Identity Created Notification"}}, {"type": "api_1_outbound"}, {"type": "api_2_outbound"}, {"type": "ccure9000_xml_outbound"}, {"type": "csv_generation_outbound"}], "order": 1}]