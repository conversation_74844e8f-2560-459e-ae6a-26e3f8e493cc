const express = require("express");
const validate = require("../middlewares/validate");
const { DeviceValidation } = require("../validations");
const { <PERSON>ceController } = require("../controllers");
const auth = require("../middlewares/auth");

const router = express.Router({ mergeParams: true });

/**
 * @swagger
 * tags:
 *   name: Facility Manager
 *   description: Device management operations
 */

/**
 * @swagger
 * /facility/devices/{facilityId}:
 *   get:
 *     summary: Get all devices for a facility
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID for which to fetch devices.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination.
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of devices per page.
 *     responses:
 *       200:
 *         description: Paginated list of devices with kiosk group details.
 *         content:
 *           application/json:
 *             example:
 *               totalItems: 15
 *               totalPages: 2
 *               currentPage: 1
 *               data:
 *                 - device_id: "64b8f0e2d123e4567890abcd"
 *                   name: "Main Lobby Kiosk 1"
 *                   identifier: "KIOSK_001"
 *                   kiosk_group_id: "74b8f0e2d123e4567890abcd"
 *                   facility_id: "84b8f0e2d123e4567890abcd"
 *                   building_id: "94b8f0e2d123e4567890abcd"
 *                   floor_id: "a4b8f0e2d123e4567890abcd"
 *                   room_id: "b4b8f0e2d123e4567890abcd"
 *                   kiosk_group: { name: "Main Lobby Kiosks" }
 *                   facility: { name: "Central Hospital" }
 *                   building: { name: "Main Building", building_code: "MB001" }
 *                   floor: { floor_number: 1 }
 *                   room: { room_number: "101A" }
 */
router.get(
  "/",
  auth("view_devices"),
  validate(DeviceValidation.facility),
  DeviceController.index
);

/**
 * @swagger
 * /facility/devices/{facilityId}/{deviceId}:
 *   get:
 *     summary: Get a specific device by ID
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID.
 *       - in: path
 *         name: deviceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The device ID.
 *     responses:
 *       200:
 *         description: Device details with kiosk group information.
 *         content:
 *           application/json:
 *             example:
 *               device_id: "64b8f0e2d123e4567890abcd"
 *               name: "Main Lobby Kiosk 1"
 *               identifier: "KIOSK_001"
 *               kiosk_group_id: "74b8f0e2d123e4567890abcd"
 *               facility_id: "84b8f0e2d123e4567890abcd"
 *               building_id: "94b8f0e2d123e4567890abcd"
 *               floor_id: "a4b8f0e2d123e4567890abcd"
 *               room_id: "b4b8f0e2d123e4567890abcd"
 *               kiosk_group: { name: "Main Lobby Kiosks" }
 *               facility: { name: "Central Hospital" }
 *               building: { name: "Main Building" }
 *               floor: { floor_number: 1 }
 *               room: { room_number: "101A" }
 *       404:
 *         description: Device not found.
 */
router.get(
  "/:deviceId",
  auth("device_details"),
  validate(DeviceValidation.device),
  DeviceController.show
);

/**
 * @swagger
 * /facility/devices/{facilityId}:
 *   post:
 *     summary: Create a new device in a facility
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID where the device will be created.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             name: "Main Lobby Kiosk 1"
 *             identifier: "KIOSK_001"
 *             kiosk_group_id: "74b8f0e2d123e4567890abcd"
 *             building_id: "94b8f0e2d123e4567890abcd"
 *             floor_id: "a4b8f0e2d123e4567890abcd"
 *             room_id: "b4b8f0e2d123e4567890abcd"
 *     responses:
 *       201:
 *         description: Device created successfully.
 */
router.post(
  "/",
  auth("create_device"),
  validate(DeviceValidation.create),
  DeviceController.create
);

/**
 * @swagger
 * /facility/devices/{facilityId}/{deviceId}:
 *   patch:
 *     summary: Update a device
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID.
 *       - in: path
 *         name: deviceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The device ID to update.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             name: "Updated Kiosk Name"
 *             identifier: "KIOSK_001_UPDATED"
 *             kiosk_group_id: "74b8f0e2d123e4567890abcd"
 *             building_id: "94b8f0e2d123e4567890abcd"
 *             floor_id: "a4b8f0e2d123e4567890abcd"
 *             room_id: "b4b8f0e2d123e4567890abcd"
 *     responses:
 *       200:
 *         description: Device updated successfully.
 */
router.patch(
  "/:deviceId",
  auth("edit_device"),
  validate(DeviceValidation.update),
  DeviceController.update
);

/**
 * @swagger
 * /facility/devices/{facilityId}/{deviceId}:
 *   delete:
 *     summary: Delete a device by ID
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID
 *       - in: path
 *         name: deviceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The device ID to delete
 *     responses:
 *       200:
 *         description: Device deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Device deleted successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     device_id:
 *                       type: string
 *                       example: "64b8f0e2d123e4567890mnop"
 *       404:
 *         description: Device not found for the given facility
 *       401:
 *         description: Unauthorized
 */
router.delete("/:deviceId", auth("delete_device"), validate(DeviceValidation.remove), DeviceController.remove);

module.exports = router;
