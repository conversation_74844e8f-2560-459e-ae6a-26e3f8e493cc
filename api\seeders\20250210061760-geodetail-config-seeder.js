"use strict";
const { v4: uuidv4 } = require("uuid");
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Seed Countries
      const countries = [
        {
          country_id: uuidv4(),
          name: "United States",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          country_id: uuidv4(),
          name: "Canada",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          country_id: uuidv4(),
          name: "India",
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];
      await queryInterface.bulkInsert("country", countries, { transaction });
      // Map country names to their IDs for States
      const countryMap = {};
      countries.forEach((country) => {
        countryMap[country.name] = country.country_id;
      });
      // Seed States
      const usStates = [
        { name: "Alabama", country_id: countryMap["United States"] },
        { name: "Alaska", country_id: countryMap["United States"] },
        { name: "Arizona", country_id: countryMap["United States"] },
        { name: "Arkansas", country_id: countryMap["United States"] },
        { name: "California", country_id: countryMap["United States"] },
        { name: "Colorado", country_id: countryMap["United States"] },
        { name: "Connecticut", country_id: countryMap["United States"] },
        { name: "Delaware", country_id: countryMap["United States"] },
        { name: "Florida", country_id: countryMap["United States"] },
        { name: "Georgia", country_id: countryMap["United States"] },
        { name: "Hawaii", country_id: countryMap["United States"] },
        { name: "Idaho", country_id: countryMap["United States"] },
        { name: "Illinois", country_id: countryMap["United States"] },
        { name: "Indiana", country_id: countryMap["United States"] },
        { name: "Iowa", country_id: countryMap["United States"] },
        { name: "Kansas", country_id: countryMap["United States"] },
        { name: "Kentucky", country_id: countryMap["United States"] },
        { name: "Louisiana", country_id: countryMap["United States"] },
        { name: "Maine", country_id: countryMap["United States"] },
        { name: "Maryland", country_id: countryMap["United States"] },
        { name: "Massachusetts", country_id: countryMap["United States"] },
        { name: "Michigan", country_id: countryMap["United States"] },
        { name: "Minnesota", country_id: countryMap["United States"] },
        { name: "Mississippi", country_id: countryMap["United States"] },
        { name: "Missouri", country_id: countryMap["United States"] },
        { name: "Montana", country_id: countryMap["United States"] },
        { name: "Nebraska", country_id: countryMap["United States"] },
        { name: "Nevada", country_id: countryMap["United States"] },
        { name: "New Hampshire", country_id: countryMap["United States"] },
        { name: "New Jersey", country_id: countryMap["United States"] },
        { name: "New Mexico", country_id: countryMap["United States"] },
        { name: "New York", country_id: countryMap["United States"] },
        { name: "North Carolina", country_id: countryMap["United States"] },
        { name: "North Dakota", country_id: countryMap["United States"] },
        { name: "Ohio", country_id: countryMap["United States"] },
        { name: "Oklahoma", country_id: countryMap["United States"] },
        { name: "Oregon", country_id: countryMap["United States"] },
        { name: "Pennsylvania", country_id: countryMap["United States"] },
        { name: "Rhode Island", country_id: countryMap["United States"] },
        { name: "South Carolina", country_id: countryMap["United States"] },
        { name: "South Dakota", country_id: countryMap["United States"] },
        { name: "Tennessee", country_id: countryMap["United States"] },
        { name: "Texas", country_id: countryMap["United States"] },
        { name: "Utah", country_id: countryMap["United States"] },
        { name: "Vermont", country_id: countryMap["United States"] },
        { name: "Virginia", country_id: countryMap["United States"] },
        { name: "Washington", country_id: countryMap["United States"] },
        { name: "West Virginia", country_id: countryMap["United States"] },
        { name: "Wisconsin", country_id: countryMap["United States"] },
        { name: "Wyoming", country_id: countryMap["United States"] },
      ];
      const states = usStates.map((state) => ({
        state_id: uuidv4(),
        name: state.name,
        country_id: state.country_id, // Link each state to its country
        created_at: new Date(), // Add created_at
        updated_at: new Date(), // Add updated_at
      }));
      await queryInterface.bulkInsert("state", states, { transaction });
      // Seed Timezones (US)
      const usTimezones = [
        { code: "PST", country_id: countryMap["United States"] },
        { code: "MST", country_id: countryMap["United States"] },
        { code: "CST", country_id: countryMap["United States"] },
        { code: "EST", country_id: countryMap["United States"] },
        { code: "AKST", country_id: countryMap["United States"] },
        { code: "HAST", country_id: countryMap["United States"] },
        { code: "AST", country_id: countryMap["United States"] },
        { code: "ChST", country_id: countryMap["United States"] },
      ];
      const timezones = usTimezones.map((tz) => ({
        timezone_id: uuidv4(),
        code: tz.code,
        country_id: tz.country_id, // Link each timezone to its country
        created_at: new Date(), // Add created_at
        updated_at: new Date(), // Add updated_at
      }));
      await queryInterface.bulkInsert("timezone", timezones, { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.bulkDelete("timezone", null, { transaction });
      await queryInterface.bulkDelete("state", null, { transaction });
      await queryInterface.bulkDelete("country", null, { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};