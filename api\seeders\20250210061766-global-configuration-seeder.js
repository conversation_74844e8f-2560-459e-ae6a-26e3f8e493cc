"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const globalConfiguration = [
        {
          global_configuration_id: uuidv4(),
          name: "patient_max_visitors",
          display_name: "Max Patient Visitor Allowed",
          value: "3",
          description: "This will denote the maximum limit of allowed patients",
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];
      await queryInterface.bulkInsert("global_configuration", globalConfiguration, { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.bulkDelete("global_configuration", { name: "patient_max_visitors" }, { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
