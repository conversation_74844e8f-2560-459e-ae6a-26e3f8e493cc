"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface) => {
    await queryInterface.bulkInsert('language', [
      {
        language_id: uuidv4(),
        name: 'English',
        code: 'en',
        default: true,
      },
      {
        language_id: uuidv4(),
        name: 'Spanish',
        code: 'es',
        default: false,
      },
    ]);
  },

  down: async (queryInterface) => {
    await queryInterface.bulkDelete('language', null, {});
  },
};
