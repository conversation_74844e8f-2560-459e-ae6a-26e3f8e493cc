"use strict"
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface) => {
    const [notifications] = await queryInterface.sequelize.query(`
      SELECT notification_id FROM notification
      WHERE name = 'Send Check In notification'
      LIMIT 1;
    `);
    const [inpatientAdmission] = await queryInterface.sequelize.query(`
      SELECT notification_id FROM notification
      WHERE name = 'Inpatient Admission Notification'
      LIMIT 1;
    `);
    const [sendVisit] = await queryInterface.sequelize.query(`
      SELECT notification_id FROM notification
      WHERE name = 'Send Visit notification'
      LIMIT 1;
    `);
    if (!notifications || notifications.length === 0) {
      throw new Error('Notification "Send Check In notification" not found');
    }
    const notification = notifications[0];
    await queryInterface.bulkInsert('notification_novu', [
      {
        notification_novu_id: uuidv4(),
        notification_id: notification.notification_id,
        workflow_id: 'send-check-in-notification',
        receiver_email: 'email',
        subscriber_id: 'patient_id',
        receiver_phone: 'phone',
        receiver_first_name: 'first_name',
        receiver_last_name: 'last_name',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        notification_novu_id: uuidv4(),
        notification_id: inpatientAdmission[0].notification_id,
        workflow_id: 'inpatient-admission-notification',
        subscriber_id: 'patient_id',
        receiver_email: 'email',
        receiver_phone: 'phone',
        receiver_first_name: 'first_name',
        receiver_last_name: 'last_name',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        notification_novu_id: uuidv4(),
        notification_id: sendVisit[0].notification_id,
        workflow_id: 'send-visit-notification',
        subscriber_id: 'visit_id',
        receiver_email: 'host_email',
        receiver_phone: 'host_phone',
        receiver_first_name: 'host_first_name',
        receiver_last_name: 'host_last_name',
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },
  down: async (queryInterface) => {
    await queryInterface.bulkDelete('notification_novu', null, {});
  },
}