const Joi = require("joi");
const { exists } = require("./custom.validation");
const models = require("../models");

// Reusable identifiers
const kioskGroupId = Joi.string()
  .required()
  .external(exists("KioskGroup", "kiosk_group_id"));
const kioskSettingId = Joi.string()
  .required()
  .external(exists("KioskSetting", "kiosk_setting_id"));
const kioskGroupSettingId = Joi.string()
  .required()
  .external(exists("KioskGroupSetting", "kiosk_group_setting_id"));

const kioskGroup = {
  params: Joi.object().keys({
    kioskGroupId,
  }),
};

const create = {
  params: Joi.object().keys({
    kioskGroupId,
  }),
  body: Joi.object()
    .keys({
      settings: Joi.array()
        .items(
          Joi.object().keys({
            kiosk_setting_id: kioskSettingId,
            config_value: Joi.string().optional(),
          })
        )
        .min(1)
        .required(),
    })
    .external(async (value, helpers) => {
      // Check for duplicate kiosk_setting_id within the request
      const settingIds = value.settings.map(setting => setting.kiosk_setting_id);
      const uniqueSettingIds = [...new Set(settingIds)];
      if (settingIds.length !== uniqueSettingIds.length) {
        return helpers.error('Duplicate kiosk_setting_id values are not allowed in the same request');
      }

      // Bulk check for existing combinations in database
      const kioskGroupId = helpers.state.ancestors[0].params.kioskGroupId;
      const existingSettings = await models.KioskGroupSetting.findAll({
        where: {
          kiosk_group_id: kioskGroupId,
          kiosk_setting_id: settingIds,
        },
        attributes: ['kiosk_setting_id'],
      });

      if (existingSettings.length > 0) {
        const existingIds = existingSettings.map(s => s.kiosk_setting_id);
        return helpers.error(`Settings already exist for kiosk_setting_id: ${existingIds.join(', ')}`);
      }

      return value;
    }),
};

const kioskGroupSetting = {
  params: Joi.object().keys({
    kioskGroupId,
    kioskGroupSettingId,
  }),
};

const update = {
  params: Joi.object().keys({
    kioskGroupId,
  }),
  body: Joi.object()
    .keys({
      settings: Joi.array()
        .items(
          Joi.object().keys({
            kiosk_group_setting_id: kioskGroupSettingId,
            config_value: Joi.string().required(),
          })
        )
        .min(1)
        .required(),
    })
    .external(async (value, helpers) => {
      // Check for duplicate kiosk_group_setting_id within the request
      const settingIds = value.settings.map(setting => setting.kiosk_group_setting_id);
      const uniqueSettingIds = [...new Set(settingIds)];
      if (settingIds.length !== uniqueSettingIds.length) {
        return helpers.error('Duplicate kiosk_group_setting_id values are not allowed in the same request');
      }

      // Bulk verify all settings belong to the specified kiosk group
      const kioskGroupId = helpers.state.ancestors[0].params.kioskGroupId;
      const existingSettings = await models.KioskGroupSetting.findAll({
        where: {
          kiosk_group_setting_id: settingIds,
          kiosk_group_id: kioskGroupId,
        },
        attributes: ['kiosk_group_setting_id'],
      });

      if (existingSettings.length !== settingIds.length) {
        const foundIds = existingSettings.map(s => s.kiosk_group_setting_id);
        const missingIds = settingIds.filter(id => !foundIds.includes(id));
        return helpers.error(`Settings do not belong to the specified kiosk group: ${missingIds.join(', ')}`);
      }

      return value;
    }),
};

const updateSingle = {
  params: Joi.object().keys({
    kioskGroupId,
    kioskGroupSettingId,
  }),
  body: Joi.object()
    .keys({
      config_value: Joi.string().optional(),
    })
    .min(1),
};

const remove = {
  params: Joi.object().keys({
    kioskGroupId,
    kioskGroupSettingId,
  }),
};

module.exports = {
  kioskGroup,
  create,
  kioskGroupSetting,
  update,
  updateSingle,
  remove,
};
