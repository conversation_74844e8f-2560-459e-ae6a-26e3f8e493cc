const Joi = require("joi");

module.exports = {
  createGuest: {
    body: Joi.object().keys({
      first_name: Jo<PERSON>.string().required().max(100).description("Guest's first name"),
      last_name: Jo<PERSON>.string().required().max(100).description("Guest's last name"),
      email: Joi.string().email().required().max(255).description("Guest's email address"),
      mobile_phone: Joi.string().optional().allow("").max(20).description("Guest's mobile phone number"),
      company: Joi.string().optional().allow("").max(200).description("Guest's company name"),
      private_visitor: Joi.boolean().optional().default(false).description("Whether the guest is a private visitor"),
      created_by: Joi.string().uuid().optional().allow(""),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },

  updateGuest: {
    params: Joi.object().keys({
      guest_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      first_name: Joi.string().optional().max(100).description("Guest's first name"),
      last_name: Joi.string().optional().max(100).description("Guest's last name"),
      email: Joi.string().email().optional().max(255).description("Guest's email address"),
      mobile_phone: Joi.string().optional().allow("").max(20).description("Guest's mobile phone number"),
      company: Joi.string().optional().allow("").max(200).description("Guest's company name"),
      private_visitor: Joi.boolean().optional().description("Whether the guest is a private visitor"),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },

  getGuestById: {
    params: Joi.object().keys({
      guest_id: Joi.string().uuid().required(),
    }),
  },

  deleteGuest: {
    params: Joi.object().keys({
      guest_id: Joi.string().uuid().required(),
    }),
  },

  getGuests: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      search: Joi.string().allow("").optional(),
    }),
  },

  searchGuests: {
    query: Joi.object().keys({
      search: Joi.string().required(),
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(10),
    }),
  },

  // ==================== VISIT VALIDATIONS ====================

  createVisit: {
    body: Joi.object().keys({
      facility_id: Joi.string().uuid().required().description("Facility ID where visit takes place"),
      host_id: Joi.string().uuid().required().description("Host identity ID"),
      escort_id: Joi.string().uuid().optional().allow("").description("Escort identity ID (optional)"),
      start_date: Joi.date().required().description("Visit start date"),
      start_time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/).required().description("Visit start time (HH:MM:SS)"),
      duration: Joi.number().integer().min(1).required().description("Visit duration in minutes"),
      created_by: Joi.string().uuid().optional().allow(""),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },

  updateVisit: {
    params: Joi.object().keys({
      visit_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      facility_id: Joi.string().uuid().optional().description("Facility ID where visit takes place"),
      host_id: Joi.string().uuid().optional().description("Host identity ID"),
      escort_id: Joi.string().uuid().optional().allow("").description("Escort identity ID (optional)"),
      start_date: Joi.date().optional().description("Visit start date"),
      start_time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/).optional().description("Visit start time (HH:MM:SS)"),
      duration: Joi.number().integer().min(1).optional().description("Visit duration in minutes"),
      purpose: Joi.string().optional().allow("").max(500).description("Purpose of the visit"),
      status: Joi.string().optional().valid("Scheduled", "In Progress", "Completed", "Cancelled").description("Visit status"),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },

  getVisitById: {
    params: Joi.object().keys({
      visit_id: Joi.string().uuid().required(),
    }),
  },

  deleteVisit: {
    params: Joi.object().keys({
      visit_id: Joi.string().uuid().required(),
    }),
  },

  getVisits: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      facility_id: Joi.string().uuid().optional(),
      guest_id: Joi.string().uuid().optional(),
      host_id: Joi.string().uuid().optional(),
      status: Joi.string().valid("Scheduled", "In Progress", "Completed", "Cancelled").optional(),
      start_date: Joi.date().optional(),
    }),
  },
  updateImage: {
    params: Joi.object().keys({
      guest_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      image: Joi.string().required(),
    }),
  }
};
