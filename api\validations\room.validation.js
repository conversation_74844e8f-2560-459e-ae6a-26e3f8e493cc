const Joi = require("joi");
const { exists } = require("./custom.validation");

const facilityId = Joi.string().required().external(exists("Facility", "facility_id"));
const floorId = Joi.string().required().external(exists("Floor", "floor_id"));

const floor_id = Joi.string().optional().external(exists("Floor", "floor_id"));
const building_id = Joi.string().required().external(exists("Building", "building_id"));
const roomId = Joi.string().required().external(exists("Room", "room_id"));

// For room status, use a master data key (number) rather than a string.
const create = {
  params: Joi.object().keys({
    facilityId,
  }),
  body: Joi.object().keys({
    floor_id,
    building_id,
    room_number: Joi.string().required(),
    max_occupancy: Joi.number().integer().required(),
    area: Joi.number().integer().required(),
    primary_contact_name: Joi.string().required(),
    primary_contact_number: Joi.string().required().pattern(/^\d+$/),
    primary_contact_email: Joi.string().email().required(),
    status: Joi.number().integer().optional(), // Should later be validated via existsMasterData("room_status") if desired.
  }),
};

const room = {
  params: Joi.object().keys({
    facilityId,
    roomId,
  }),
};

const update = {
  params: Joi.object().keys({
    facilityId,
    roomId,
  }),
  body: Joi.object()
    .keys({
      floor_id,
      building_id,
      room_number: Joi.string().optional(),
      max_occupancy: Joi.number().integer().optional(),
      area: Joi.number().integer().optional(),
      primary_contact_name: Joi.string().optional(),
      primary_contact_number: Joi.string().optional().pattern(/^\d+$/),
      primary_contact_email: Joi.string().email().optional(),
      status: Joi.number().integer().optional(),
    })
    .min(1),
};

const status = {
  params: Joi.object().keys({
    facilityId,
    roomId,
  }),
  body: Joi.object().keys({
    status: Joi.number().integer().required(),
  }),
};

const facility = {
  params: Joi.object().keys({
    facilityId,
  }),
};

const floor = {
  params: Joi.object().keys({
    floorId,
  }),
};

const remove = {
  params: Joi.object().keys({
    facilityId,
    roomId,
  }),
};

module.exports = {
  create,
  room,
  update,
  status,
  facility,
  floor,
  remove,
};
