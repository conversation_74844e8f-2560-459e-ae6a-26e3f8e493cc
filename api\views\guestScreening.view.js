module.exports = (sequelize, DataTypes) => {
    const GuestScreeningView = sequelize.define(
      "GuestScreeningView",
      {
        appointment_guest_id: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        appointment_guest_screening_id: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        appointment_guest_screening_match_id: {
          type: DataTypes.UUID,
          allowNull: false,
          primaryKey: true, 
        },
        first_name: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        last_name: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        dob: {
          type: DataTypes.DATE,
          allowNull: true,
        },
        gender: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        hair_color: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        added_by: {
          type: DataTypes.UUID,
          allowNull: true, 
        },
        match_type: {
          type: DataTypes.STRING, // 'd' for Denied Guest, 'w' for Watchlist
          allowNull: false,
        },
      },
      {
        tableName: "view_guest_screening_matches",
        timestamps: false,
        freezeTableName: true,
      }
    );
  
    return GuestScreeningView;
  };
  