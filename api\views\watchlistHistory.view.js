module.exports = (sequelize, DataTypes) => {
    const WatchlistHistoryView = sequelize.define(
      "WatchlistHistoryView",
      {
        watchlist_id:{
            type: DataTypes.UUID,
            primaryKey: true,
            allowNull: false,
        },
        effective_date: {
          type: DataTypes.DATE,
          allowNull: false,
        },

        watchlist_history_id: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        field_changes: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        old_value: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        new_value: {
          type: DataTypes.STRING,
          allowNull: true,
        },
        event_type: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        modified_by: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      },
      {
        tableName: "view_watchlist_history",
        timestamps: false,
        underscored: true,
      }
    );
  
    return WatchlistHistoryView;
  };
  