[2025-07-25 11:39:26] [32minfo[39m: Successfully connected to RabbitMQ and channel is ready
[2025-07-25 11:39:30] [32minfo[39m: Using in-memory caching
[2025-07-25 11:39:36] [32minfo[39m: Executing (default): SELECT 1+1 AS result
[2025-07-25 11:39:37] [32minfo[39m: Connected to PostgreSQL
[2025-07-25 11:39:37] [32minfo[39m: Listening to port 3001 on all interfaces
[2025-07-25 11:40:24] [32minfo[39m: GET /docs/ 304 - 48.464 ms
[2025-07-25 11:40:24] [32minfo[39m: GET /docs/swagger-ui.css 200 - 30.884 ms
[2025-07-25 11:40:25] [32minfo[39m: GET /docs/swagger-ui-standalone-preset.js 200 - 24.864 ms
[2025-07-25 11:40:25] [32minfo[39m: GET /docs/swagger-ui-init.js 200 - 27.056 ms
[2025-07-25 11:40:25] [32minfo[39m: GET /docs/swagger-ui-bundle.js 200 - 22.673 ms
[2025-07-25 11:40:29] [32minfo[39m: GET /docs/favicon-32x32.png 200 - 2.397 ms
[2025-07-25 11:40:40] [32minfo[39m: Executing (default): SELECT "identity_id", "facility_id", "email", "first_name", "last_name", "middle_name", "eid", "identity_type", "national_id", "suffix", "mobile", "start_date", "end_date", "status", "suspension", "suspension_date", "reason", "image", "company", "organization", "company_code", "job_title", "job_code", "manager", "updated_by", "created_at" AS "createdAt", "updated_at" AS "updatedAt", "created_by" FROM "identity" AS "Identity" WHERE "Identity"."email" = '<EMAIL>';
[2025-07-25 11:40:42] [32minfo[39m: Executing (df5d66be-f214-45a2-9f03-daf0dfb50025): START TRANSACTION;
[2025-07-25 11:40:42] [32minfo[39m: Executing (df5d66be-f214-45a2-9f03-daf0dfb50025): INSERT INTO "event_trace" ("trace_id","endpoint","created_at") VALUES ($1,$2,$3) RETURNING "trace_id","endpoint","function_id","created_at";
[2025-07-25 11:40:42] [32minfo[39m: Executing (default): SELECT "identity_id", "facility_id", "email", "first_name", "last_name", "middle_name", "eid", "identity_type", "national_id", "suffix", "mobile", "start_date", "end_date", "status", "suspension", "suspension_date", "reason", "image", "company", "organization", "company_code", "job_title", "job_code", "manager", "updated_by", "created_at" AS "createdAt", "updated_at" AS "updatedAt", "created_by" FROM "identity" AS "Identity" WHERE "Identity"."email" = '<EMAIL>';
[2025-07-25 11:40:43] [32minfo[39m: Executing (default): SELECT "identity_verification_id", "identity_id", "password_hash", "updated_by", "created_at" AS "createdAt", "updated_at" AS "updatedAt", "created_by" FROM "identity_verification" AS "IdentityVerification" WHERE "IdentityVerification"."identity_id" = '87dbfd8e-9b10-4e82-a95f-cbfc3a546acf' LIMIT 1;
[2025-07-25 11:40:43] [32minfo[39m: Executing (default): SELECT "Role"."role_id", "Role"."name", "Role"."description", "Role"."is_active", "Role"."updated_by", "Role"."created_at" AS "createdAt", "Role"."updated_at" AS "updatedAt", "Role"."created_by", "permission"."permission_id" AS "permission.permission_id", "permission"."name" AS "permission.name", "IdentityRole"."identity_role_id" AS "IdentityRole.identity_role_id", "IdentityRole"."identity_id" AS "IdentityRole.identity_id", "IdentityRole"."role_id" AS "IdentityRole.role_id", "IdentityRole"."updated_by" AS "IdentityRole.updated_by", "IdentityRole"."created_at" AS "IdentityRole.createdAt", "IdentityRole"."updated_at" AS "IdentityRole.updatedAt", "IdentityRole"."created_by" AS "IdentityRole.created_by" FROM "role" AS "Role" LEFT OUTER JOIN ( "role_permission" AS "permission->RolePermission" INNER JOIN "permission" AS "permission" ON "permission"."permission_id" = "permission->RolePermission"."permission_id") ON "Role"."role_id" = "permission->RolePermission"."role_id" INNER JOIN "identity_role" AS "IdentityRole" ON "Role"."role_id" = "IdentityRole"."role_id" AND "IdentityRole"."identity_id" = '87dbfd8e-9b10-4e82-a95f-cbfc3a546acf';
[2025-07-25 11:40:44] [32minfo[39m: Executing (df5d66be-f214-45a2-9f03-daf0dfb50025): INSERT INTO "token" ("id","token","identity_id","type","expires","blacklisted","createdAt","updatedAt") VALUES (DEFAULT,$1,$2,$3,$4,$5,$6,$7) RETURNING "id","token","identity_id","type","expires","blacklisted","createdAt","updatedAt";
[2025-07-25 11:40:45] [32minfo[39m: Executing (default): SELECT "LanguagePreference"."language_preference_id", "LanguagePreference"."identity_id", "LanguagePreference"."language_id", "LanguagePreference"."updated_by", "LanguagePreference"."created_by", "language"."language_id" AS "language.language_id", "language"."name" AS "language.name", "language"."code" AS "language.code", "language"."default" AS "language.default", "language"."status" AS "language.status", "language"."addons" AS "language.addons", "language"."updated_by" AS "language.updated_by", "language"."created_by" AS "language.created_by" FROM "language_preference" AS "LanguagePreference" LEFT OUTER JOIN "language" AS "language" ON "LanguagePreference"."language_id" = "language"."language_id" WHERE "LanguagePreference"."identity_id" = '87dbfd8e-9b10-4e82-a95f-cbfc3a546acf';
[2025-07-25 11:40:45] [32minfo[39m: Executing (default): SELECT "language_id", "name", "code", "default", "status", "addons", "updated_by", "created_by" FROM "language" AS "Language" WHERE "Language"."default" = true LIMIT 1;
[2025-07-25 11:40:45] [32minfo[39m: Executing (df5d66be-f214-45a2-9f03-daf0dfb50025): INSERT INTO "activity_log" ("id","identity_id","action","metadata","ip_address","user_agent","device_id","created_at","updated_at") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING "id","identity_id","action","metadata","ip_address","user_agent","device_id","created_at","updated_at";
[2025-07-25 11:40:46] [32minfo[39m: Executing (df5d66be-f214-45a2-9f03-daf0dfb50025): COMMIT;
[2025-07-25 11:40:46] [32minfo[39m: POST /api/auth/login 200 - 7716.551 ms
[2025-07-25 11:41:38] [32minfo[39m: SIGINT received
[2025-07-25 11:42:47] [32minfo[39m: Successfully connected to RabbitMQ and channel is ready
[2025-07-25 11:42:50] [32minfo[39m: Using in-memory caching
[2025-07-25 11:42:56] [32minfo[39m: Executing (default): SELECT 1+1 AS result
[2025-07-25 11:42:56] [32minfo[39m: Connected to PostgreSQL
[2025-07-25 11:42:56] [32minfo[39m: Listening to port 3001 on all interfaces
[2025-07-25 11:49:38] [32minfo[39m: Successfully connected to RabbitMQ and channel is ready
[2025-07-25 11:49:41] [32minfo[39m: Using in-memory caching
