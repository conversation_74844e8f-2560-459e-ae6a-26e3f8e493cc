import React, { useState } from "react";
import Button from "../Global/Button";

const OwnersAdd = ({ onSubmit, onClose, availableOwners }) => {
  const defaultOwners = [
    { name: "<PERSON>", eid: "E001", type: "Employee", organization: "Org1", jobTitle: "Developer", status: "Active" },
    { name: "<PERSON>", eid: "E002", type: "Employee", organization: "Org1", jobTitle: "Designer", status: "Inactive" },
    { name: "<PERSON>", eid: "E003", type: "Manager", organization: "Org2", jobTitle: "Project Manager", status: "Active" },
    { name: "<PERSON>", eid: "E004", type: "Employee", organization: "Org2", jobTitle: "QA Engineer", status: "Active" },
    { name: "<PERSON>", eid: "E005", type: "Employee", organization: "Org3", jobTitle: "Support", status: "Inactive" },
    { name: "<PERSON>", eid: "E006", type: "Employee", organization: "Org4", jobTitle: "Analyst", status: "Active" },
    { name: "<PERSON>", eid: "E007", type: "Manager", organization: "Org4", jobTitle: "Team Lead", status: "Active" },
    { name: "Fiona Blue", eid: "E008", type: "Employee", organization: "Org5", jobTitle: "Consultant", status: "Active" },
  ];

  const ownersList = availableOwners && availableOwners.length ? availableOwners : defaultOwners;
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedOwner, setSelectedOwner] = useState("");
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [show, setShow] = useState(false);

  // Smooth open animation
  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 50);
    return () => clearTimeout(timer);
  }, []);

  // Filter owners based on the search term
  const filteredOwners = ownersList.filter((owner) =>
    owner.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectOwner = (ownerName) => {
    setSelectedOwner(ownerName);
    setSearchTerm(ownerName);
    setIsDropdownVisible(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedOwner) {
      alert("Please select an owner.");
      return;
    }
    const selectedOwnerObj = ownersList.find((owner) => owner.name === selectedOwner);
    onSubmit(selectedOwnerObj);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-bold text-[#4F2683]">Add Owner / Employee</h2>
          <button
            type="button"
            className="w-8 h-8 bg-[#4F2683] text-white flex items-center justify-center rounded-full text-2xl"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">
            {/* Form */}
            <form onSubmit={handleSubmit} className="bg-white p-6 pt-2 rounded-lg my-3">
              {/* Search Input with Dropdown */}
              <div className="mb-4 flex items-center">
                <label className="text-[16px] font-normal w-1/4">Select Owner</label>
                <div className="relative w-3/4">
                  <input
                    type="text"
                    placeholder="Search Owner"
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                      setIsDropdownVisible(true);
                    }}
                    onFocus={() => setIsDropdownVisible(true)}
                    onBlur={() => setTimeout(() => setIsDropdownVisible(false), 150)}
                    className="w-full h-11 border border-gray-300 rounded px-3"
                  />
                  {isDropdownVisible && (
                    <div className="absolute top-full left-0 w-full mt-1 border bg-white rounded-md shadow-lg max-h-60 overflow-y-auto z-50">
                      {filteredOwners.length > 0 ? (
                        filteredOwners.map((owner) => (
                          <div
                            key={owner.name}
                            className="p-2 cursor-pointer hover:bg-gray-100"
                            onMouseDown={() => handleSelectOwner(owner.name)}
                          >
                            {owner.name}
                          </div>
                        ))
                      ) : (
                        <div className="p-2 text-gray-700 text-center">No Results Found.</div>
                      )}
                    </div>
                  )}
                </div>
              </div>
              {/* Action Buttons */}
              <div className="flex gap-4 justify-center">
                <Button type="button" label="Cancel" onClick={() => {
                  setShow(false);
                  setTimeout(onClose, 700);
                }} className="bg-gray-400 text-white" />
                <Button type="submit" label="Add" className="bg-[#4F2683] text-white" />
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OwnersAdd;
