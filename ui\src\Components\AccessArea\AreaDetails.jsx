import React, { useState } from 'react';
import EditableSection from '../../Components/Global/EditableSection';

const AreaDetails = () => {
  const [details, setDetails] = useState({
    AreaName: 'Consultant',
    System: 'Active',
    FriendlyName: '12345',
    AreaType: 'Commercial',
    AreaId: '123',
    Status: 'Active',
  });

  const [configuration, setConfiguration] = useState({
    RequestableByAdmin: 'Yes',
    RequestableInSelfService: 'Yes',
    AreaSpecialInstruction: 'Access group for GPS Executives only',
  });

  const handleInputChange = (section, key, value) => {
    if (section === 'details') {
      setDetails((prev) => ({ ...prev, [key]: value }));
    } else if (section === 'configuration') {
      setConfiguration((prev) => ({ ...prev, [key]: value }));
    }
  };

  return (
    <div className="bg-gray-100 min-h-screen">
      {/* Only AreaType and FriendlyName will be editable in this section */}
      <EditableSection
        title="Details"
        data={details}
        onChange={(key, value) => handleInputChange('details', key, value)}
        editableKeys={['AreaType', 'FriendlyName']}
      />

      <EditableSection
        title="Configuration"
        data={configuration}
        onChange={(key, value) => handleInputChange('configuration', key, value)}
        dropdownKeys={['RequestableByAdmin', 'RequestableInSelfService']}
        dropdownOptions={{
          RequestableByAdmin: ['Yes', 'No'],
          RequestableInSelfService: ['Yes', 'No'],
        }}
      />
    </div>
  );
};

export default AreaDetails;
