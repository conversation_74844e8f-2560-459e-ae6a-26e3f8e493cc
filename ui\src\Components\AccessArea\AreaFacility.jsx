import React, { useState } from "react";
import GenericTable from "../GenericTable";
import AccessAreaAdd from "./AccessAreaAdd";
import ViewEditAreaFacilityForm from "../Facility/AccessArea/ViewEditAccessAreaForm";
import Delete from "../../Images/Delete.svg";


const AreaFacility = () => {
  const [data, setData] = useState([
    {
      id: 1,
      facility: "Facility A",
      code: "FAC001",
    
      city: "New York",
      state: "NY",
      country: "USA",
      status: "Active",
    },
    {
      id: 2,
      facility: "Facility B",
      code: "FAC002",
    
      city: "Los Angeles",
      state: "CA",
      country: "USA",
      status: "Inactive",
    },
    {
      id: 3,
      facility: "Facility C",
      code: "FAC003",
    
      city: "Chicago",
      state: "IL",
      country: "USA",
      status: "Active",
    },
    {
      id: 4,
      facility: "Facility D",
      code: "FAC004",
   
      city: "Houston",
      state: "TX",
      country: "USA",
      status: "expired",
    },
    {
      id: 5,
      facility: "Facility E",
      code: "FAC005",
    
      city: "Miami",
      state: "FL",
      country: "USA",
      status: "Active",
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [showAddForm, setShowAddForm] = useState(false);
  const [showViewForm, setShowViewForm] = useState(false);
  const [selectedFacility, setSelectedFacility] = useState(null);

  // Função para deletar um item com base no id
  const handleDeleteOwner = (id) => {
    setData((prevData) => prevData.filter((item) => item.id !== id));
  };

  const columns = [
    {
      name: "Facility",
      selector: (row) => row.facility,
      sortable: true,
    },
    {
      name: "Code",
      selector: (row) => row.code,
    },
    {
      name: "City",
      selector: (row) => row.city,
    },
    {
      name: "State",
      selector: (row) => row.state,
    },
    {
      name: "Country",
      selector: (row) => row.country,
    },
    {
      name: "Action",
      cell: (row) => (
        <img src={Delete} alt="Delete" 
         className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer" onClick={() => handleDeleteOwner(row.id)} />
      ),
    },
  ];

  const handleAdd = () => {
    setShowAddForm(true);
  };

  const handleCloseAddModal = () => {
    setShowAddForm(false);
  };

  const handleView = (row) => {
    setSelectedFacility(row);
    setShowViewForm(true);
  };

  const handleUpdate = (updatedFacility) => {
    setData((prevData) =>
      prevData.map((item) =>
        item.id === updatedFacility.id ? { ...item, ...updatedFacility } : item
      )
    );
    setShowViewForm(false);
  };

  const handleCloseViewModal = () => {
    setShowViewForm(false);
  };

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        title="Facility(s)"
        searchTerm={searchTerm}
        showSearch={true}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        onAdd={handleAdd}
        columns={columns}
        data={data}
        showAddButton={true}
      />
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg">
            <div className="rounded-lg max-h-[90vh]">
              <AccessAreaAdd
                onClose={handleCloseAddModal}
                onSubmit={(newFacility, action) => {
                  setData([newFacility, ...data]);
                  if (action === "add") {
                    setShowAddForm(false);
                  }
                }}
              />
            </div>
          </div>
        </div>
      )}
      {showViewForm && selectedFacility && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-10 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewEditAreaFacilityForm
                onClose={handleCloseViewModal}
                facilityData={selectedFacility}
                onUpdate={handleUpdate}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AreaFacility;
