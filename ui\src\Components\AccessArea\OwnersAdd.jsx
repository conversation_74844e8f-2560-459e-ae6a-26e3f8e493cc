import React, { useState } from "react";
import Button from "../Global/Button";

const OwnersAdd = ({ onSubmit, onClose, availableOwners }) => {
  const defaultOwners = [
    { name: "<PERSON>", eid: "E001", type: "Employee", organization: "Org1", jobTitle: "Developer", status: "Active" },
    { name: "<PERSON>", eid: "E002", type: "Employee", organization: "Org1", jobTitle: "Designer", status: "Inactive" },
    { name: "<PERSON>", eid: "E003", type: "Manager", organization: "Org2", jobTitle: "Project Manager", status: "Active" },
    { name: "<PERSON>", eid: "E004", type: "Employee", organization: "Org2", jobTitle: "QA Engineer", status: "Active" },
    { name: "<PERSON>", eid: "E005", type: "Employee", organization: "Org3", jobTitle: "Support", status: "Inactive" },
    { name: "<PERSON>", eid: "E006", type: "Employee", organization: "Org4", jobTitle: "Analyst", status: "Active" },
    { name: "<PERSON>", eid: "E007", type: "Manager", organization: "Org4", jobTitle: "Team Lead", status: "Active" },
    { name: "Fiona Blue", eid: "E008", type: "Employee", organization: "Org5", jobTitle: "Consultant", status: "Active" },
  ];

  const ownersData = availableOwners && availableOwners.length ? availableOwners : defaultOwners;

  // We'll store the entire owner object when one is selected.
  const [selectedOwner, setSelectedOwner] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // Filter owners by matching search term with name, type, or eid.
  const filteredOwners = ownersData.filter(owner => {
    const term = searchTerm.toLowerCase();
    return (
      owner.name.toLowerCase().includes(term) ||
      owner.type.toLowerCase().includes(term) ||
      owner.eid.toLowerCase().includes(term)
    );
  });

  const handleOwnerInputChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    setIsDropdownVisible(value.length > 0);
    // Clear the previously selected owner if user starts typing
    if (selectedOwner && !value.includes(selectedOwner.eid)) {
      setSelectedOwner(null);
    }
  };

  const handleOwnerSelect = (owner) => {
    setSelectedOwner(owner);
    setSearchTerm(`${owner.name} - ${owner.type} - ${owner.eid}`);
    setIsDropdownVisible(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedOwner) {
      alert("Please select an owner.");
      return;
    }
    onSubmit(selectedOwner);
    setShow(false);
    setTimeout(onClose, 700);
  };

  return (
   <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Owner</h2>
           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        {/* Form */}
        <form onSubmit={handleSubmit} className="bg-white p-6 pt-2 rounded-lg my-3">
          {/* Owner Search and Dropdown */}
          <div className="flex items-center mb-4">
            <label htmlFor="owner" className="text-[16px] font-normal w-1/4">
              Select Owner*
            </label>
            <div className="relative w-3/4">
              <input
                type="text"
                id="owner"
                placeholder="Search Owner"
                value={searchTerm}
                onChange={handleOwnerInputChange}
                onFocus={() => setIsDropdownVisible(true)}
                onBlur={() => setTimeout(() => setIsDropdownVisible(false), 150)}
                className="w-full h-11 border border-gray-300 rounded px-3"
              />
              {isDropdownVisible && (
                <div className="absolute w-full mt-1 border bg-white rounded-md shadow-lg max-h-60 overflow-y-auto z-10">
                  {filteredOwners.length > 0 ? (
                    filteredOwners.map((owner) => (
                      <div
                        key={owner.eid}
                        className="p-2 cursor-pointer hover:bg-gray-100"
                        onMouseDown={() => handleOwnerSelect(owner)}
                      >
                        {owner.name} - {owner.type} - {owner.eid}
                      </div>
                    ))
                  ) : (
                    <div className="p-2 text-gray-700 text-center">
                      No Results Found.
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 justify-center">
            <Button
              type="button"
              label="Cancel"
              onClick={onClose}
              className="bg-gray-400 text-white"
            />
            <Button
              type="submit"
              label="Add"
              className="bg-[#4F2683] text-white"
            />
          </div>
        </form>
      </div>
      </div>
      </div>
    </div>
  );
};

export default OwnersAdd;
