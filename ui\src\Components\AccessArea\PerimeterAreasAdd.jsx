import React, { useState } from "react";
import Button from "../Global/Button";

function PerimeterAreasAdd({ onSubmit, onClose, availableAreas }) {
  // Default sample area data (6–8 entries)
  const defaultAreas = [
    { areaName: "Area 501", areaType: "Office", system: "HVAC" },
    { areaName: "Area 502", areaType: "Conference", system: "Lighting" },
    { areaName: "Area 503", areaType: "Lab", system: "Security" },
    { areaName: "Area 504", areaType: "Storage", system: "Fire" },
    { areaName: "Area 505", areaType: "Office", system: "IT" },
    { areaName: "Area 506", areaType: "Break Room", system: "Cafeteria" },
    { areaName: "Area 507", areaType: "Meeting", system: "Audio" },
    { areaName: "Area 508", areaType: "Office", system: "HVAC" },
  ];

  const areasList =
    availableAreas && availableAreas.length ? availableAreas : defaultAreas;

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedArea, setSelectedArea] = useState("");
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // Filter areas based on the search term
  const filteredAreas = areasList.filter((area) =>
    area.areaName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectArea = (areaName) => {
    setSelectedArea(areaName);
    setSearchTerm(areaName);
    setIsDropdownVisible(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedArea) {
      alert("Please select an area.");
      return;
    }
    const selectedAreaObj = areasList.find(
      (area) => area.areaName === selectedArea
    );
    onSubmit(selectedAreaObj);
    setShow(false);
    setTimeout(onClose, 700);
  };

  return (
   <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]"> Add PerimeterArea</h2>
           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        {/* Form */}
        <form
          onSubmit={handleSubmit}
          className="bg-white p-6 pt-2 rounded-lg my-3"
        >
          {/* Area Search Input with Dropdown */}
          <div className="mb-4 flex items-center">
            <label className="text-[16px] font-normal w-1/4">
              Select Area
            </label>
            <div className="relative w-3/4">
              <input
                type="text"
                placeholder="Search Area"
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setIsDropdownVisible(true);
                }}
                onFocus={() => setIsDropdownVisible(true)}
                onBlur={() =>
                  setTimeout(() => setIsDropdownVisible(false), 150)
                }
                className="w-full h-11 border border-gray-300 rounded px-3"
              />
              {isDropdownVisible && (
                <div className="absolute top-full left-0 w-full mt-1 border bg-white rounded-md shadow-lg max-h-60 overflow-y-auto z-50">
                  {filteredAreas.length > 0 ? (
                    filteredAreas.map((area) => (
                      <div
                        key={area.areaName}
                        className="p-2 cursor-pointer hover:bg-gray-100"
                        onMouseDown={() => handleSelectArea(area.areaName)}
                      >
                        {area.areaName}
                      </div>
                    ))
                  ) : (
                    <div className="p-2 text-gray-700 text-center">
                      No Results Found.
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 justify-center">
            <Button
              type="button"
              label="Cancel"
              onClick={onClose}
              className="bg-gray-400 text-white"
            />
            <Button
              type="submit"
              label="Add"
              className="bg-[#4F2683] text-white"
            />
          </div>
        </form>
      </div>
      </div>
      </div>
    </div>
  );
}

export default PerimeterAreasAdd;
