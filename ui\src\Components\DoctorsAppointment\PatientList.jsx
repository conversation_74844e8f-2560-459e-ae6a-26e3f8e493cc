import React, { useState } from "react";
import DataTable from "react-data-table-component";

const PatientList = ({ data }) => {
    const [searchQuery, setSearchQuery] = useState("");
    const [entriesPerPage, setEntriesPerPage] = useState(5);

    const filteredPatients = Array.isArray(data)
        ? data.filter((patient) =>
            patient.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
        : [];

    const customStyles = {
        headCells: {
            style: {
                fontSize: "14px",
                fontWeight: "bold",
                color: "#7c3aed",
                backgroundColor: "#fafafa",
                padding: "12px",
                borderRadius: "none"
            },
        },
        cells: {
            style: {
                padding: "12px",
            },
        },
    };

    const columns = [
        {
            name: "Patient Name",
            selector: (row) => row.name,
            cell: (row) => (
                <div className="flex items-center">
                    <img
                        src={row.image}
                        alt={row.name}
                        className="w-10 h-10 rounded-full mr-2 border"
                    />
                    <span className="text-gray-700">{row.name}</span>
                </div>
            ),
            sortable: true,
        },
        {
            name: "MRN",
            selector: (row) => row.mrn,
            sortable: true,
        },
        {
            name: "Location",
            cell: (row) => (
                <div className="text-gray-600">
                    Room - {row.room}, Bed - {row.bed}
                </div>
            ),
            sortable: true,
        },
        {
            name: "Appointment Time",
            selector: (row) => row.appointmentTime,
            sortable: true,
        },
    ];

    return (
        <>
        <div className="bg-white border p-4 rounded-md shadow-lg pb-0 mt-4">
            <h2 className="text-lg mb-2 font-semibold text-gray-700">Patient List</h2>
            <div className="flex justify-between items-center pb-4 border-b">
                <div>
                    <label className="text-gray-600 text-sm">Show
                        <select 
                            className="border p-1 rounded-md mx-2"
                            value={entriesPerPage} 
                            onChange={(e) => setEntriesPerPage(Number(e.target.value))}
                        >
                            <option value="5">5</option>
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                        entries
                    </label>
                </div>
                <div>
                    <input
                        type="text"
                        placeholder="Search..."
                        className="border p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-400"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                </div>
            </div>
            <DataTable
                columns={columns}
                data={filteredPatients.slice(0, entriesPerPage)}
                customStyles={customStyles}
                highlightOnHover
                // pagination
                // paginationPerPage={entriesPerPage}
                // paginationRowsPerPageOptions={[5, 10, 25, 50]}
                defaultSortFieldId={1}
                noDataComponent={<div className="text-gray-500 p-4">No patients available</div>}
            />
        </div>
        {/* <h1>:</h1> */}
        </>

    );
};

export default PatientList;