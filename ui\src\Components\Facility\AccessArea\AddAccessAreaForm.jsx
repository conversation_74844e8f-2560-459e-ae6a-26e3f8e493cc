import React, { useState, useMemo, useEffect } from "react";
import { useForm, Controller, useWatch } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Button from "../../Global/Button";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";
import { toast } from "react-toastify";

// Hooks
import { useBuildingData } from "../../../hooks/useBuildingData";
import { useFloorData } from "../../../hooks/useFloorData";
import { useRoomData } from "../../../hooks/useRoomData";

// API
import { createAccessLevel } from "../../../api/facility";
import { getAccessLevels, getMasterData } from "../../../api/global";

const AddAccessAreaForm = ({ onClose, refreshAccessAreas, facilityId }) => {
  console.log("AddAccessAreaForm: Received facilityId:", facilityId);

  const buildingOptions = useBuildingData(facilityId);
  const [selectedBuilding, setSelectedBuilding] = useState("");
  const floorOptions = useFloorData(selectedBuilding);
  const [selectedFloor, setSelectedFloor] = useState("");
  const roomOptions = useRoomData(selectedFloor);

  console.log("AddAccessAreaForm: buildingOptions:", buildingOptions);
  console.log("AddAccessAreaForm: floorOptions:", floorOptions);
  console.log("AddAccessAreaForm: roomOptions:", roomOptions);

  // Master data states
  const [accessLevelOptions, setAccessLevelOptions] = useState([]);
  const [identityTypeOptions, setIdentityTypeOptions] = useState([]);
  const [masterDataLoading, setMasterDataLoading] = useState(true);

  useEffect(() => {
    const fetchMasterData = async () => {
      setMasterDataLoading(true);
      try {
        // Fetch access levels
        const accessLevelsData = await getAccessLevels();
        console.log("Access Levels:", accessLevelsData);
        setAccessLevelOptions(
          (accessLevelsData?.data || []).map((item) => ({ key:item?.access_level_id,label: item.name, value: item.name }))
        );

        // Fetch identity types
        console.log("Calling getMasterData...");
        const identityTypesRes = await getMasterData({ groups: "identity_type" });
        console.log("Identity Type Master Data:", identityTypesRes);
        const identityTypes = identityTypesRes?.data?.identity_type || [];
        setIdentityTypeOptions(
          identityTypes.map((item) => ({
            label: item.value,
            value: item.value
          }))
        );
      } catch (err) {
        console.error("Error fetching master data:", err);
        setAccessLevelOptions([]);
        setIdentityTypeOptions([]);
      } finally {
        setMasterDataLoading(false);
      }
    };
    fetchMasterData();
  }, []);



  const {
    register,
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm({
    // resolver: yupResolver(accessAreaSchema),
    defaultValues: {
      // facility_id: facilityId,
      building_id: "",
      floor_id: "",
      room_id: "",
      access_level_id: "",
      requestable_guest: false,
      default_access_guest: false,
      default_access_identity: false,
      identity_type: [],
    },
  });

  const requestableGuest = useWatch({ control, name: "requestable_guest" });
  const defaultAccessIdentity = useWatch({ control, name: "default_access_identity" });

  const fields = [
    {
      label: "Facility",
      name: "facility_id",
      type: "text",
      readOnly: true,
      value: facilityId,
    },
    {
      label: "Building",
      name: "building_id",
      type: "customDropdown",
      placeholder: "Select Building",
      options: buildingOptions,
      onSelectExtra: (val, onChange) => {
        setSelectedBuilding(val);
        onChange(val);
        setValue("floor_id", "");
        setSelectedFloor("");
        setValue("room_id", "");
      },
    },
    {
      label: "Floor",
      name: "floor_id",
      type: "customDropdown",
      placeholder: "Select Floor",
      options: floorOptions,
      onSelectExtra: (val, onChange) => {
        setSelectedFloor(val);
        onChange(val);
        setValue("room_id", "");
      },
    },
    {
      label: "Room No.",
      name: "room_id",
      type: "customDropdown",
      placeholder: "Select Room",
      options: roomOptions,
    },
    {
      label: "Access Level *",
      name: "access_level_id",
      type: "customDropdown",
      placeholder: "Access Level",
      options: accessLevelOptions,
    },
  ];

  const [loading, setLoading] = useState(false);

  const onSubmit = async (data) => {
    console.log("beofre submit", data);
    // Validation logic for your specific case
    const isValid =
      data.requestable_guest === true &&
      data.default_access_guest === false &&
      data.default_access_identity === true &&
      Array.isArray(data.identity_type) &&
      data.identity_type.includes("COS") &&
      data.identity_type.includes("EMP");


    setLoading(true);
    try {
      await createAccessLevel(facilityId, data);
      toast.success("Access level created successfully!");
      refreshAccessAreas();
      onClose();
    } catch {
      toast.error("Error creating access level.");
    } finally {
      setLoading(false);
    }
  };

  // Animation state
  const [show, setShow] = useState(false);

  // Mount/unmount logic for smooth open/close
  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  console.log("acces",accessLevelOptions)
  return (
   <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Access Area (Access Level)</h2>
           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">Access Level Details</h2>

          {/* Render core fields */}
          {fields.map(({ label, type, name, placeholder, options, readOnly, value, onSelectExtra }, idx) => (
            <div key={idx} className="flex items-center mb-4">
              <label className="w-1/3 text-[16px] font-normal text-[#333333]">{label}</label>
              <div className="w-2/3">
                {type === "customDropdown" ? (
                  <Controller
                    control={control}
                    name={name}
                    render={({ field }) => (
                      <CustomDropdown
                        value={field.value}
                        options={options}
                        placeholder={placeholder}
                        onSelect={(option) => {
                          console.log("access level change", option);
                          if (onSelectExtra) onSelectExtra(option, field.onChange);
                          else field.onChange(option);
                        }}
                        loading={masterDataLoading}
                        bgColor="bg-white text-black"
                        textColor="text-black"
                        hoverBgColor="hover:bg-[#4F2683]"
                        borderColor="border-gray-300"
                        className="p-2 border h-11 rounded focus:outline-none focus:ring-1"
                        rounded="rounded"
                        error={errors[name]}
                      />
                    )}
                  />
                ) : (
                  <Input
                    type="text"
                    name={name}
                    placeholder={placeholder}
                    readOnly={readOnly}
                    value={value}
                    error={errors[name]}
                    {...(name !== "facility_id" && register(name))}
                  />
                )}
                {errors[name] && <p className="text-red-500 text-sm mt-1">{errors[name]?.message}</p>}
              </div>
            </div>
          ))}

          {/* Toggle Section */}
          <div className="mb-6">
            <div className="flex justify-between w-full mb-2 px-1">
              <label className="w-1/3 text-[16px] font-normal text-[#333333]">Requestable for Guest</label>
              <label className="w-1/3 text-[16px] font-normal text-[#333333]">Default Access for Guest</label>
              <label className="w-1/3 text-[16px] font-normal text-[#333333]">Default Access for Identity</label>
            </div>
            <div className="flex justify-between w-full px-1">
              <div className="w-1/3 flex justify-center">
                <Controller
                  control={control}
                  name="requestable_guest"
                  render={({ field }) => (
                    <Button
                      type="toggle"
                      initialState={field.value}
                      onClick={(state) => field.onChange(state)}
                      label={field.value ? "Yes" : "No"}
                    />
                  )}
                />
              </div>
              <div className="w-1/3 flex justify-center">
                <Controller
                  control={control}
                  name="default_access_guest"
                  render={({ field }) => (
                    <Button
                      type="toggle"
                      initialState={field.value}
                      onClick={(state) => field.onChange(state)}
                      label={field.value ? "Yes" : "No"}
                      className={requestableGuest ? "pointer-events-none opacity-50" : ""}
                      disabled={requestableGuest}
                    />
                  )}
                />
              </div>
              <div className="w-1/3 flex justify-center">
                <Controller
                  control={control}
                  name="default_access_identity"
                  render={({ field }) => (
                    <Button
                      type="toggle"
                      initialState={field.value}
                      onClick={(state) => field.onChange(state)}
                      label={field.value ? "Yes" : "No"}
                    />
                  )}
                />
              </div>
            </div>
          </div>

          {/* Identity Type Dropdown */}
          <div className="flex items-center mb-4">
            <label className="w-1/3 text-[16px] font-normal text-[#333333]">Identity Type</label>
            <div className="w-2/3">
              <Controller
                control={control}
                name="identity_type"
                render={({ field }) => (
                  <CustomDropdown
                    value={field.value[0] || ""}
                    options={identityTypeOptions}
                    placeholder="Select Identity Type"
                    onSelect={(option) => {
                      console.log("identity change", option);
                      field.onChange([option]);

                      field.onChange([option]);
                    }}
                    bgColor="bg-white text-black"
                    textColor="text-black"
                    hoverBgColor="hover:bg-[#4F2683]"
                    borderColor="border-gray-300"
                    className={`p-2 border h-11 rounded focus:outline-none focus:ring-1 ${!defaultAccessIdentity ? "bg-gray-100 cursor-not-allowed opacity-70" : ""}`}
                    rounded="rounded"
                    error={errors.identity_type}
                    disabled={!defaultAccessIdentity}
                  />
                )}
              />
              {errors.identity_type && <p className="text-red-500 text-sm mt-1">{errors.identity_type.message}</p>}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-center gap-4 mt-6">
            <Button type="cancel" label="Cancel" onClick={onClose} />
            <Button type="primary" label={loading ? "Saving..." : "Add"} disabled={loading} />
          </div>
        </form>
        </div>
        </div>
      </div>
    </div>
  );
};

export default AddAccessAreaForm;
