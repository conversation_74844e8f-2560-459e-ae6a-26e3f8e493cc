import React, { useState, useMemo, useEffect } from "react";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Button from "../../Global/Button";
import CustomDropdown from "../../Global/CustomDropdown";
import Input from "../../Global/Input/Input";
import { toast } from "react-toastify";
import { createBuilding } from "../../../api/facility"; // API function for creating building
import { useBuildingMasterData } from "../../../hooks/useBuildingMasterData";
import { sanitizeRequest } from "../../../utils/helpers";

const AddBuildingForm = ({facility, onClose, fetchBuildings }) => {
  // Fetch building master data and dropdown options
  const { masterData, statusOptions, typeOptions, occupancyOptions } = useBuildingMasterData();

  // Define field mappings similar to your facility component
  const buildingFields = [
    {
      label: "Building Name *",
      type: "text",
      placeholder: "Building Name",
      name: "name",
    },
    {
      label: "Building Code",
      type: "text",
      placeholder: "Building Code",
      name: "building_code",
    },
    {
      label: "Status",
      type: "customDropdown",
      placeholder: "Select Status",
      name: "status",
      options: statusOptions,
    },
    {
      label: "Building Type",
      type: "customDropdown",
      placeholder: "Select Building Type",
      name: "type",
      options: typeOptions,
    },
    {
      label: "Occupancy Type",
      type: "customDropdown",
      placeholder: "Select Occupancy Type",
      name: "occupancy_type",
      options: occupancyOptions,
    },
    {
      label: "Building Phone",
      type: "text",
      placeholder: "Building Phone",
      name: "phone",
    },
    {
      label: "Building Email",
      type: "email",
      placeholder: "Building Email",
      name: "email",
    },
    {
      label: "Geo Location Code",
      type: "number",
      placeholder: "Geo Location Code",
      name: "geo_location_code",
    },
    {
      label: "Other Code",
      type: "text",
      placeholder: "Other Code",
      name: "other_code",
    },
    {
      label: "Building URL",
      type: "url",
      placeholder: "Building URL",
      name: "building_url",
    },
    {
      label: "Building Notes",
      type: "textarea",
      placeholder: "Building Notes",
      name: "notes",
    },
    {
      label: "Address",
      type: "text",
      placeholder: "Address",
      name: "address",
    },
  ];

  // Build Yup validation schema using allowed master data values (converted to numbers)
  const buildingSchema = useMemo(
    () =>
      yup.object().shape({
        name: yup.string().required("Building Name is required"),
        building_code: yup.string().nullable(),
        status: yup.number().nullable(),
        type: yup.number().nullable(),
        occupancy_type: yup.number().nullable(),
        phone: yup.string().nullable(),
        email: yup.string().nullable(),
        geo_location_code: yup.number().nullable(),
        other_code: yup.string().nullable(),
        building_url: yup.string().nullable(),
        notes: yup.string().nullable(),
        address: yup.string().nullable(),
      }),
    [masterData]
  );

  const [loading, setLoading] = useState(false);

  const {
    register,
    handleSubmit,
    control,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(buildingSchema),
  });

  // Animation state
  const [show, setShow] = useState(false);

  // Mount/unmount logic for smooth open/close
  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // Handle form submission using the createBuilding API function
  const submitFormHandler = async (data) => {
    setLoading(true);
    try {
      const sanitizeData = sanitizeRequest(data);
      const response = await createBuilding(facility.facility_id, sanitizeData);
      if (response && response.status === false) {
        throw { response: { data: { data: response.data || {} } } };
      }
      toast.success("Building added successfully!");
      fetchBuildings();
      onClose();
    } catch (error) {
      console.log(error);
      toast.error(error.response && error.response.data ? error.response.data.message : "Error adding building!");
      if (
        error.response &&
        error.response.data &&
        error.response.data.data
      ) {
        const errorsData = error.response.data.data;
        Object.keys(errorsData).forEach((field) => {
          setError(field, { type: "server", message: errorsData[field] });
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
   <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Building</h2>
           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        <form onSubmit={handleSubmit(submitFormHandler)}>
          <div className="p-6">
            <h3 className="text-[20px] text-[#333333] font-medium pb-4">Building Details</h3>
            {buildingFields.map(({ label, type, name, options, placeholder }, idx) => (
              <div key={idx} className="flex mb-2">
                <label className="mr-2 w-1/3 text-[16px] font-normal text-[#333333]">{label}</label>
                <div className="w-2/3">
                  {type === "customDropdown" ? (
                    <Controller
                      control={control}
                      name={name}
                      defaultValue={null}
                      render={({ field }) => (
                        <CustomDropdown
                          value={field.value}
                          options={options}
                          placeholder={placeholder}
                          onSelect={(option) => {
                            const numericValue =
                              typeof option === "object" ? Number(option.value) : Number(option);
                            field.onChange(numericValue);
                          }}
                          bgColor="bg-[white] text-black"
                          textColor="text-black"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="p-2 border h-11 rounded focus:outline-none focus:ring-1"
                          rounded="rounded"
                          error={errors[name]}
                        />
                      )}
                    />
                  ) : (
                    <Input
                      type={type}
                      name={name}
                      placeholder={placeholder}
                      error={errors[name]}
                      {...register(name)}
                    />
                  )}
                  {errors[name] && (
                    <p className="text-red-500 text-sm mt-1">{errors[name].message}</p>
                  )}
                </div>
              </div>
            ))}

            <div className="flex justify-center gap-4 mt-6">
              <Button type="cancel" label="Cancel" onClick={onClose} />
              <Button type="primary" label={loading ? "Saving..." : "Add"} disabled={loading} />
            </div>
          </div>
        </form>
      </div>
    </div>
      </div>
    </div>
  );
};

export default AddBuildingForm;

