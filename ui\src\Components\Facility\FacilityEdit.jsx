import React, { useState, useEffect, useMemo } from "react";
import EditableSection from "../Global/EditableSection.jsx";
import { updateFacility, updateFacilityAddress } from "../../api/facility.js";
import Loader from "../Loader.jsx";
import { useFacilityMasterData } from "../../hooks/useFacilityMasterData";
import { toast } from "react-toastify";

const Facilitery = ({ facility, refreshFacilityData }) => {
  const { facility_id } = facility; // Get dynamic facility ID from URL

  // State for facility data
  const [biographicData, setBiographicData] = useState({
    FacilityName: facility.name,
    status: facility.facility_status_name,
    facilityCode: facility.facility_code,
    facilityType: facility.facility_type_name,
    TimeZone: facility.time_zone,
    facilityPhone: facility.phone,
    facilityEmail: facility.email,
    GeoLocationCode: facility.geo_location_code,
    OtherCode: facility.other_code,
    facilityUrl: facility.facility_url,
    ConnectedApplications: facility.connected_applications,
    facilityNotes: facility.notes,
  });
  const [addressData, setAddressData] = useState({
    Address1: facility.address.address_line_1,
    Address2: facility.address.address_line_2,
    Country: facility.address.country,
    StateProvince: facility.address.state_province,
    PostalCode: facility.address.postal_code,
    MapUrl: facility.address.map_url,
    Region: facility.address.region,
  });

  const handleInputChange = (section, key, value) => {
    if (section === "biographic") {
      setBiographicData((prev) => ({ ...prev, [key]: value }));
    } else if (section === "address") {
      setAddressData((prev) => ({ ...prev, [key]: value }));
    }
  };

  // Handler to save biographic section using updateFacility API
  const handleBiographicSave = async (updatedLocalData) => {
    const updatedData = {
      name: updatedLocalData.FacilityName,
      status: updatedLocalData.status.key,
      facility_code: updatedLocalData.facilityCode,
      facility_type: updatedLocalData.facilityType.key,
      time_zone: updatedLocalData.TimeZone,
      phone: updatedLocalData.facilityPhone,
      email: updatedLocalData.facilityEmail,
      geo_location_code: updatedLocalData.GeoLocationCode,
      other_code: updatedLocalData.OtherCode,
      facility_url: updatedLocalData.facilityUrl,
      connected_applications: updatedLocalData.ConnectedApplications,
      notes: updatedLocalData.facilityNotes,
    };
    try {
      await updateFacility(facility_id, updatedData);
      toast.success("Facility data updated successfully");
      refreshFacilityData();
    } catch (error) {
      toast.error(error.response && error.response.data
        ? error.response.data.message
        : "Error updating biographic data");
    }
  };

  // Handler to save address section using updateFacility API
  const handleAddressSave = async (updatedLocalData) => {
    const updatedData = {
        address_line_1: updatedLocalData.Address1,
        address_line_2: updatedLocalData.Address2,
        country: updatedLocalData.Country,
        state_province: updatedLocalData.StateProvince,
        postal_code: updatedLocalData.PostalCode,
        map_url: updatedLocalData.MapUrl,
        region: updatedLocalData.Region,
    };

    try {
      await updateFacilityAddress(facility_id, updatedData);
      toast.success("Facility address updated successfully");
      refreshFacilityData();
    } catch (error) {
      toast.error(error.response && error.response.data
        ? error.response.data.message
        : "Error updating address data");
    }
  };
  const { facilityStatusOptions, facilityTypeOptions } = useFacilityMasterData();

  if (!useFacilityMasterData) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader />
      </div>
    );
  }


  return (
    <div className="bg-gray-100 p-0 ">
      <EditableSection
        title="Biographic"
        data={biographicData}
        onChange={(key, value) => handleInputChange("biographic", key, value)}
        onSave={handleBiographicSave}
        dropdownKeys={["status", "facilityType"]}
        dropdownOptions={{
          status: facilityStatusOptions,
          facilityType: facilityTypeOptions,
        }}
      />

      <EditableSection
        title="Address"
        data={addressData}
        onChange={(key, value) => handleInputChange("address", key, value)}
        onSave={handleAddressSave}
        dropdownKeys={["Country", "StateProvince"]}
        searchableKeys={["Country", "StateProvince"]}
        dropdownOptions={{
          Country: [
            "India",
            "USA",
            "Nepal",
            "Bhutan",
            "France",
            "Czech Republic",
          ],
          StateProvince: [
            "State 1",
            "State 2",
            "State 3",
            "State 4",
            "State 5",
            "State 6",
          ],
        }}
      />
    </div>
  );
};

export default Facilitery;
