import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import CustomDropdown from "../../Global/CustomDropdown";
import Input from "../../Global/Input/Input";
import Loader from "../../Loader.jsx";
import { toast } from "react-toastify";
import { useFloorMasterData } from "../../../hooks/useFloorMasterData";
import { useBuildingData } from "../../../hooks/useBuildingData.js";
import { updateFloor } from "../../../api/facility.js";

// Validation schema for floor fields
const floorSchema = yup.object().shape({
  facility_id: yup.string().required("Facility is required"),
  building_id: yup.string().required("Building is required"),
  floor_number: yup.string().required("Floor Number is required"),
  status: yup.number().required("Status is required"),
  total_square_footage: yup
    .number()
    .typeError("Total Sq Ft must be a number")
    .required("Total Sq Ft is required"),
  max_occupancy: yup
    .number()
    .typeError("Max Occupancy must be a number")
    .required("Max Occupancy is required"),
  occupancy_type: yup.number().required("Occupancy Type is required"),
});

const ViewEditFloorForm = ({
  facility,
  floorData,
  refreshFloors,
  onClose,
}) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [show, setShow] = useState(false);
  const { statusOptions, occupancyOptions } = useFloorMasterData();

  // Static building options (example). Adjust or replace with dynamic data as needed.
  const buildingOptions = useBuildingData(facility.facility_id);

  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(floorSchema),
    defaultValues: {
      floor_id: floorData.floor_id || "",
      building_id: floorData.building_id || "",
      facility_id: floorData.facility_id || facility.facility_id || "",
      floor_number: floorData.floor_number || "",
      status: floorData.status,
      total_square_footage: floorData.total_square_footage || "",
      max_occupancy: floorData.max_occupancy || "",
      occupancy_type: floorData.occupancy_type,
    },
  });

  // Reset the form when floorData changes
  useEffect(() => {
    reset({
      floor_id: floorData.floor_id || "",
      building_id: floorData.building_id || "",
      facility_id: floorData.facility_id || facility.facility_id || "",
      floor_number: floorData.floor_number || "",
      status: floorData.status,
      total_square_footage: floorData.total_square_footage || "",
      max_occupancy: floorData.max_occupancy || "",
      occupancy_type: floorData.occupancy_type,
    });
  }, [floorData, facility, reset]);

  // Animation mount/unmount logic
  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // Field configuration array
  const floorFields = [
    {
      label: "Facility",
      name: "facility_id",
      type: "text",
      isReadOnly: true,
      viewValue: floorData.facility?.name || facility.name,
    },
    {
      label: "Building",
      name: "building_id",
      type: "dropdown",
      options: buildingOptions,
      viewValue: floorData.building?.name || floorData.building_id,
    },
    {
      label: "Floor Number",
      name: "floor_number",
      type: "text",
    },
    {
      label: "Status",
      name: "status",
      type: "dropdown",
      options: statusOptions,
      viewValue: floorData.floor_status_name?.value || floorData.status,
    },
    {
      label: "Total Sq Ft",
      name: "total_square_footage",
      type: "number",
    },
    {
      label: "Max Occupancy",
      name: "max_occupancy",
      type: "number",
    },
    {
      label: "Occupancy Type",
      name: "occupancy_type",
      type: "dropdown",
      options: occupancyOptions,
      viewValue:
        floorData.floor_occupancy_type_name?.value || floorData.occupancy_type,
    },
  ];

  // Form submission handler with sequential API calls
  const onSubmit = async (data) => {
    try {
      const { facility_id, floor_id, ...payload } = data;
      // Wait for the updateFloor API call to complete
      await updateFloor(facility_id, floor_id, payload);
      toast.success("Floor updated successfully!");
      // Ensure the floor list is refreshed before closing
      await refreshFloors();
      setIsEditMode(false);
      onClose();
    } catch (error) {
      toast.error(
        error.response && error.response.data
          ? error.response.data.message
          : "Failed to update floor. Please try again."
      );
    }
  };

  // Cancel editing: reset form values to original data
  const handleCancelEdit = () => {
    reset({
      floor_id: floorData.floor_id || "",
      building_id: floorData.building_id || "",
      facility_id: floorData.facility_id || facility.facility_id || "",
      floor_number: floorData.floor_number || "",
      status: floorData.status,
      total_square_footage: floorData.total_square_footage || "",
      max_occupancy: floorData.max_occupancy || "",
      occupancy_type: floorData.occupancy_type,
    });
    setIsEditMode(false);
  };

  return (
   <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">View/Edit Floor</h2>
           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6">
          {floorFields.map((field, idx) => (
            <div key={idx} className="flex items-center mb-4">
              <label className="w-1/3 text-[16px] font-normal">{field.label}</label>
              <div className="w-2/3">
                {isEditMode && !field.isReadOnly ? (
                  field.type === "dropdown" ? (
                    <Controller
                      control={control}
                      name={field.name}
                      render={({ field: controllerField }) => (
                        <CustomDropdown
                          key={controllerField.value}
                          className="h-11 rounded border-gray-300"
                          placeholder={field.placeholder || `Select ${field.label}`}
                          options={field.options}
                          onSelect={controllerField.onChange}
                          selectedOption={controllerField.value}
                          value={controllerField.value}
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                        />
                      )}
                    />
                  ) : (
                    <Input
                      type={field.type}
                      {...register(field.name)}
                      className="w-full border rounded p-2"
                    />
                  )
                ) : (
                  <Input
                    type={field.type === "dropdown" ? "text" : field.type}
                    value={
                      field.type === "dropdown"
                        ? field.viewValue
                        : floorData[field.name] || ""
                    }
                    disabled
                    className="w-full border-none text-[#8F8F8F]"
                  />
                )}
                {errors[field.name] && (
                  <p className="text-red-500 text-sm">{errors[field.name].message}</p>
                )}
              </div>
            </div>
          ))}

          {/* Footer Buttons */}
          <div className="flex justify-end gap-4 mt-6">
            {!isEditMode ? (
              <button
                type="button"
                onClick={() => setIsEditMode(true)}
                className="px-4 py-2 bg-[#4F2683] text-white rounded"
              >
                Edit
              </button>
            ) : (
              <>
                <button
                  type="button"
                  onClick={handleCancelEdit}
                  className="px-4 py-2 bg-[#979797] text-white rounded"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[#4F2683] text-white rounded"
                >
                  Save
                </button>
              </>
            )}
          </div>
        </form>
        </div>
        </div>
      </div>
    </div>
  );
};

export default ViewEditFloorForm;
