import React, { useState, useEffect, useMemo } from "react";
import { useParams } from "react-router-dom";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Button from "../../Global/Button";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";
import { toast } from "react-toastify";
import { createRoom } from "../../../api/facility";
import { useBuildingData } from "../../../hooks/useBuildingData";
import { useFloorData } from "../../../hooks/useFloorData";
import { useRoomMasterData } from "../../../hooks/useRoomMasterData";

const AddRoomForm = ({ onClose, fetchRooms }) => {
  const { facilityId } = useParams();
  const buildingOptions = useBuildingData(facilityId);
  const { statusOptions } = useRoomMasterData();

  // Use local state for building_id instead of using watch from useForm.
  const [selectedBuildingId, setSelectedBuildingId] = useState("");
  
  // Get floor options based on the current selected building.
  const floorOptions = useFloorData(selectedBuildingId);

  // Define the validation schema using Yup.
  const roomSchema = useMemo(
    () =>
      yup.object().shape({
        building_id: yup.string().required("Building is required"),
        floor_id: yup
          .string()
          .required("Floor Number is required")
          .matches(
            /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/,
            "Floor ID must be a valid UUID"
          ),
        room_number: yup.string().required("Room Number is required"),
        max_occupancy: yup
          .number()
          .typeError("Max Occupancy must be a number")
          .required("Max Occupancy is required"),
        primary_contact_name: yup.string().required("Primary Contact Name is required"),
        primary_contact_email: yup
          .string()
          .email("Enter a valid email")
          .required("Primary Contact Email is required"),
        primary_contact_number: yup.string().required("Primary Contact Number is required"),
        area: yup
          .number()
          .typeError("Area must be a number")
          .required("Area is required"),
        status: yup
          .number()
          .required("Status is required")
          .oneOf(statusOptions.map((opt) => Number(opt.value)), "Invalid status"),
        building_code: yup.string().required("Building Code is required"),
      }),
    [statusOptions]
  );

  // Setup the form.
  const {
    register,
    handleSubmit,
    control,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(roomSchema),
    defaultValues: {
      building_id: "",
      floor_id: "",
      room_number: "",
      max_occupancy: "",
      primary_contact_name: "",
      primary_contact_email: "",
      primary_contact_number: "",
      area: "",
      status: "",
      building_code: "",
    },
  });

  // Define field mapping.
  const roomFields = [
    {
      label: "Building *",
      name: "building_id",
      type: "customDropdown",
      placeholder: "Select Building",
      options: buildingOptions,
      onSelectExtra: (selectedId) => {
        setSelectedBuildingId(selectedId);
        setValue("building_id", selectedId);
        setValue("floor_id", "");
        const selectedBuilding = buildingOptions.find(b => b.value === selectedId);
        setValue("building_code", selectedBuilding ? selectedBuilding.code || "" : "");
      },
    },
    {
      label: "Floor Number *",
      name: "floor_id",
      type: "customDropdown",
      placeholder: "Select Floor",
      options: floorOptions,
    },
    {
      label: "Room Number *",
      name: "room_number",
      type: "text",
      placeholder: "Room Number",
    },
    {
      label: "Max Occupancy *",
      name: "max_occupancy",
      type: "number",
      placeholder: "Max Occupancy",
    },
    {
      label: "Primary Contact Name *",
      name: "primary_contact_name",
      type: "text",
      placeholder: "Primary Contact Name",
    },
    {
      label: "Primary Contact Email *",
      name: "primary_contact_email",
      type: "email",
      placeholder: "Primary Contact Email",
    },
    {
      label: "Primary Contact Number *",
      name: "primary_contact_number",
      type: "tel",
      placeholder: "Primary Contact Number",
    },
    {
      label: "Area *",
      name: "area",
      type: "number",
      placeholder: "Area",
    },
    {
      label: "Status *",
      name: "status",
      type: "customDropdown",
      placeholder: "Select Status",
      options: statusOptions,
    },
    {
      label: "Building Code *",
      name: "building_code",
      type: "text",
      placeholder: "Building Code",
      readOnly: true,
    },
  ];

  const [loading, setLoading] = useState(false);

  // Animation state
  const [show, setShow] = useState(false);

  // Mount/unmount logic for smooth open/close
  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // Handle form submission.
  const onSubmit = async (data) => {
    setLoading(true);
    try {
      if (!facilityId) {
        toast.error("Facility ID is missing. Please try again.");
        return;
      }
      // Prepare the payload.
      const { building_code, ...payload } = data;
      // console.log(payload);
      const response = await createRoom(facilityId, payload);
      if (response && response.status === false) {
        throw { response: { data: { data: response.data || {} } } };
      }
      toast.success("Room added successfully!");
      fetchRooms();
      onClose();
    } catch (error) {
      toast.error(
        error.response && error.response.data
          ? error.response.data.message
          : "Error adding room!"
      );
      if (error.response && error.response.data && error.response.data.data) {
        const errorsData = error.response.data.data;
        Object.keys(errorsData).forEach((field) => {
          setError(field, { type: "server", message: errorsData[field] });
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Room</h2>
           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">Room Details</h2>

          {roomFields.map(
            ({ label, type, name, options, placeholder, readOnly, onSelectExtra }, idx) => (
              <div key={idx} className="flex items-center mb-4">
                <label className="w-1/3 text-[16px] font-normal">{label}</label>
                <div className="w-2/3">
                  {type === "customDropdown" ? (
                    <Controller
                      control={control}
                      name={name}
                      defaultValue=""
                      render={({ field }) => (
                        <CustomDropdown
                          value={field.value}
                          options={options}
                          placeholder={placeholder}
                          onSelect={(option) => {
                            if (onSelectExtra) {
                              onSelectExtra(option, field.onChange);
                            } else {
                              field.onChange(
                                typeof option === "object" ? option.value : option
                              );
                            }
                          }}
                          bgColor="bg-white text-black"
                          textColor="text-black"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="p-2 border h-11 rounded focus:outline-none focus:ring-1"
                          rounded="rounded"
                          error={errors[name]}
                        />
                      )}
                    />
                  ) : (
                    <Input
                      type={type}
                      name={name}
                      placeholder={placeholder}
                      readOnly={readOnly}
                      error={errors[name]}
                      className="w-full border rounded p-2 hide-number-spin"
                      {...register(name)}
                    />
                  )}
                  {errors[name] && (
                    <p className="text-red-500 text-sm mt-1">{errors[name].message}</p>
                  )}
                </div>
              </div>
            )
          )}

          {/* Buttons */}
          <div className="flex justify-end gap-4 mt-6">
            <Button type="cancel" label="Cancel" onClick={onClose} />
            <Button type="primary" label={loading ? "Saving..." : "Add"} disabled={loading} />
          </div>
        </form>
        </div>
        </div>
      </div>
    </div>
  );
};

export default AddRoomForm;