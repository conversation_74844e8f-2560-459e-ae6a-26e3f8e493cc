import React, { useState, useEffect } from "react";
import Tick from "../../Images/tick.svg";
import Cross from "../../Images/cross.svg";

const Button = ({
  buttonType,
  type,
  label,
  icon,
  onClick,
  rounded,
  className,
  initialState,
  ...rest
}) => {
  const buttonClasses = {
    primary: "bg-[#4F2683] hover:bg-[#6A3BAA] text-white ",
    secondary: "bg-[#107C0F] hover:bg-[#149E13] text-white",
    danger: "bg-red-500 hover:bg-red-600 text-white",
    success: "bg-green-500 hover:bg-green-600 text-white",
    outline: "border border-[#4F2683] text-[#4F2683] hover:bg-blue-100",
    active: "bg-[#4F2683] text-white hover:bg-[#6A3BAA]",
    toggle: "flex items-center w-16 h-7 rounded-full",
    imgbtn: "rounded-lg bg-gray-100",
    close:
      "flex items-center justify-center bg-[#4F2683] text-white px-0 rounded-full h-8 w-8 hover:bg-[#6A3BAA]",
    underline: "text-[#4F2683] hover:underline",
    cancel:
      "flex items-center justify-center bg-[#979797] text-white rounded-full hover:bg-[#979797]",
  };

  const [isOn, setIsOn] = useState(initialState);

  useEffect(() => {
    setIsOn(initialState);
  }, [initialState]);

  const handleToggle = () => {
    if (type === "toggle") {
      setIsOn(!isOn);
      if (onClick) onClick(!isOn);
    } else {
      if (onClick) onClick();
    }
  };

  if (type === "toggle") {
    return (
      <div
        className={`relative ${buttonClasses.toggle} ${
          isOn ? "bg-gray-300" : "bg-gray-300"
        } ${rest.disabled ? "cursor-not-allowed opacity-50" : "cursor-pointer"}`}
        onClick={!rest.disabled ? handleToggle : undefined}
      >
        <span
          className={`absolute top-1/2 transform -translate-y-1/2 transition-all duration-300 ${
            isOn ? "left-9" : "left-1"
          }`}
        >
          <img
            src={isOn ? Tick : Cross}
            alt={isOn ? "tick" : "cross"}
            className="w-4 h-4"
          />
        </span>
      </div>
    );
  }

  if (type === "yesnoToggle") {
    return (
      <div
        className={` ${buttonClasses.toggle} ${
          isOn ? "bg-[#EDE9F6]" : "bg-gray-300"
        } flex items-center px-2`}
        onClick={handleToggle}
      >
        <span className="flex items-center">
          {isOn ? (
            <img src={Tick} alt="Yes" className="w-5 h-5 mr-2" />
          ) : (
            <img src={Cross} alt="No" className="w-5 h-5 mr-2" />
          )}
          <span
            className={`text-sm font-medium ${
              isOn ? "text-[#4F2683]" : "text-gray-500"
            } select-none`}
          >
            {isOn ? "Yes" : "No"}
          </span>
        </span>
      </div>
    );
  }

  return (
    <button
      type={buttonType}
      className={`px-6 py-2 ${buttonClasses[type]} ${
        rounded ? "rounded-full" : "rounded-lg"
      } ${className} font-normal`}
      onClick={handleToggle}
      {...rest}
    >
      {icon ? <img src={icon} alt="icon" /> : label}
    </button>
  );
};

export default Button;
