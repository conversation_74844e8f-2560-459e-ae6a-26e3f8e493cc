import React, { useState, useEffect, useRef } from "react";
import { getAppointments, fetchAllGuests } from "../../api/Appointments";
import Input from "./Input/Input";
import SearchBar from "./SearchBar";
import formatDateTime from "../../utils/formatDate";
import demoimg from "../../Images/demoimg.svg"; // Placeholder image
const CreateVisitModal = ({ isOpen, onClose, onSubmit }) => {
  const [appointments, setAppointments] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const containerRef = useRef(null);
  const [appointmentId, setAppointmentId] = useState("");
  const [patientId, setPatientId] = useState("");

  useEffect(() => {
    if (!isOpen) {
      setSearchTerm("");
      setSearchResults([]);
      setIsDropdownVisible(false);
      setSelectedPatient(null);
      setAppointmentId("");
      setPatientId("");
    }
  }, [isOpen]);

  const handleInputChange = async (value) => {
    setSearchTerm(value);
    if (value.trim()) {
      try {
        const response = await getAppointments({ search: value });
        setSearchResults(response?.data || []);
        setIsDropdownVisible(true);
      } catch (error) {
        console.error("Dropdown API error:", error);
      }
    } else {
      setSearchResults([]);
      setIsDropdownVisible(false);
    }
  };

  const handlePatientClick = (patient) => {
    setAppointmentId(patient.appointment_id);
    setPatientId(patient.patient_id);
    setSelectedPatient(patient);
    setIsDropdownVisible(false);
    setSearchTerm(patient.patient_name);
    
    // Immediately submit and close
    onSubmit(patient); // Pass selected patient data to parent
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg w-1/3">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Patient Search</h2>
          <button
            onClick={onClose}
            className="w-8 h-8 bg-[#4F2683] flex justify-center items-center p-0 text-white text-2xl rounded-full"
          >
            &times;
          </button>
        </div>
        
        <p className="text-sm mb-4">
          You are a first-time visitor. Please{" "}
          <span className="text-[#4F2683] font-semibold">Search By Patient</span>
        </p>
        
        <div className="relative mb-1" ref={containerRef}>

          <SearchBar
         placeholder="Search by Patient Name, MRN"
          iconSrc={""}
          // onChange={(e) => handleInputChange(e.target.value)}
          onInputChange={handleInputChange}
          value={searchTerm}
          borderColor="#4F2683"
          inputClassName="w-[90px]"
          
        />
          {isDropdownVisible && (
            <div
              className="absolute w-full mt-2 border bg-white z-10 rounded-md shadow-lg overflow-y-auto"
              style={{ maxHeight: "200px" }}
            >
              {searchResults.map((appointment) => (
                <div
                  key={appointment.appointment_id}
                  className="flex items-center gap-2 p-2 border-b cursor-pointer hover:bg-gray-100"
                  onClick={() => handlePatientClick(appointment)}
                >
                   <img
                src={appointment.image || demoimg}
                alt={appointment.patient_name}
                className="w-10 h-10 rounded-full"
              />
                  <div>
                    <h2 className="font-semibold">{appointment.patient_name}</h2>
                    <p className="text-[12px] text-gray-600">
                    {formatDateTime(appointment.birth_date)}, MRN: {appointment.mrn}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateVisitModal;