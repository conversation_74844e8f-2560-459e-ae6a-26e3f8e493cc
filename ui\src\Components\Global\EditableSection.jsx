import React, { useState, useEffect } from 'react';
import Input from './Input/Input';
import Button from './Button';
import CustomDropdown from './CustomDropdown';
import { FaPencil } from "react-icons/fa6";
import DateInput from './Input/DateInput';

const EditableSection = ({
  title,
  data,
  onChange,
  onSave, // Optional prop for external save functionality
  dropdownKeys = [],
  dropdownOptions = {},
  dateKeys = [],
  searchableKeys = [], // New prop for searchable fields
  editableKeys,
  dropDownclassName,
  hideEditIcon = false, // New prop to hide the edit icon
}) => {
  const displayValue = (val) => {
    if (typeof val === 'object' && val !== null) {
      return val.value || val.label || "";
    }
    return val;
  };
  const [isEditing, setIsEditing] = useState(false);
  const [localData, setLocalData] = useState(data);

  // Update localData whenever the data prop changes
  useEffect(() => {
    setLocalData(data);
  }, [data]);

  // Helper function to format key labels (e.g., facilityName -> Facility Name)
  const formatKey = (key) => {
    const withSpaces = key.replace(/([A-Z])/g, ' $1');
    return withSpaces.charAt(0).toUpperCase() + withSpaces.slice(1).trim();
  };

  const handleInputChange = (key, value) => {
    setLocalData((prev) => ({ ...prev, [key]: value }));
  };

  const handleSave = () => {
    Object.entries(localData).forEach(([key, value]) => onChange(key, value));
    // Call external onSave function if provided
    if (onSave) {
      onSave(localData);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setLocalData(data);
    setIsEditing(false);
  };

  // Determines if a field is editable
  const isFieldEditable = (key) => {
    if (editableKeys && Array.isArray(editableKeys)) {
      return editableKeys.includes(key);
    }
    return true; // Default: all fields editable if editableKeys is not provided
  };

  console.log("🚀 ~ EditableSection ~ localData:", localData);

  return (
    <div>
      <div className="bg-white px-4 pb-2 p-2 mb-2 shadow-[0px_3.94px_7.88px_4.93px_#4F26830F] rounded-lg my-1">
        <div className="flex justify-between items-center">
          <h3 className="font-[500] text-[14px] text-[#4F2683] font-poppins">{title}</h3>
          {!isEditing && !hideEditIcon && (
            <button
              onClick={() => setIsEditing(true)}
              className="rounded-full bg-[#4F2683] hover:bg-[#701ED8] p-1 mt-2"
            >
              <FaPencil className="text-white p-1" size={24} />
            </button>
          )}
        </div>
        <hr className="my-2" />
        <div>
          {isEditing ? (
            <>
              {Object.entries(localData).map(([key, value]) => (
                <div className="flex items-start mb-2" key={key}>
                  {/* Label Section */}
                  <div className="w-1/4 p-2">
                    <label
                      className="block text-[#7C7C7C] text-[12px] font-[400] font-poppins"
                      htmlFor={key}
                    >
                      {formatKey(key)}
                    </label>
                  </div>

                  {/* Input/Display Section */}
                  <div className="w-3/4">
                    {isFieldEditable(key) ? (
                      dropdownKeys.includes(key) ? (
                        <CustomDropdown
                          options={dropdownOptions[key] || []}
                          value={displayValue(value) || ''}
                          onSelect={(option) => handleInputChange(key, option)}
                          placeholder="Select an option"
                          // Pass searchable prop if key is in searchableKeys
                          searchable={searchableKeys.includes(key)}
                          // Optional styling props:
                          bgColor="bg-white"
                          textColor="text-black"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="w-full h-10 text-[12px] font-[400] text-[#000] font-poppins"
                          rounded="rounded-md"
                          dropDownclassName={dropDownclassName}
                        />
                      ) : dateKeys.includes(key) || 
                          ['DateTimeofDeceased', 'PatientDeceased', 'DischargeDate', 'AdmissionDate'].includes(key) ? (
                        <DateInput
                          className="text-[12px] font-[400] text-[#000] font-poppins"
                          value={
                            // Only pass a valid date string or empty string
                            !value || value === '-' || isNaN(new Date(value).getTime())
                              ? ''
                              : value
                          }
                          onChange={(date) => handleInputChange(key, date)}
                        />
                      ) : (
                        <Input
                          type="text"
                          className="text-[12px] font-[400] text-[#000] font-poppins"
                          id={key}
                          label=""
                          value={value || ''}
                          onChange={(e) => handleInputChange(key, e.target.value)}
                        />
                      )
                    ) : (
                      // For non-editable fields in edit mode, just display the value
                      <p className="text-[12px] font-[400] text-[#000] font-poppins">{value}</p>
                    )}
                  </div>
                </div>
              ))}
            </>
          ) : (
            <div>
              {Object.entries(localData).map(([key, value]) => (
                <div className="flex items-start mb-2" key={key}>
                  {/* Key Section */}
                  <div className="w-1/4">
                    <p className="text-[#7C7C7C] font-poppins text-[12px]">{formatKey(key)}</p>
                  </div>
                  {/* Value Section */}
                  <div className="w-3/4">
                    <p className="text-[#000] font-[400] font-poppins text-[12px]">
                      {value && (typeof value === 'object' ? (value.value || value.label) : value)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      {isEditing && (
        <div className="flex gap-2 mb-4 justify-end">
          <Button type="cancel" label="Cancel" onClick={handleCancel} />
          <Button type="primary" label="Save Change" onClick={handleSave} />
        </div>
      )}
    </div>
  );
};

export default EditableSection;
