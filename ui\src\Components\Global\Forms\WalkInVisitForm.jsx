import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Button from "../Button";
import ImageCapture from "../ImageAndCamera/ImageCaptureForForm";
import Input from "../Input/Input";
import DateInput from "../Input/DateInput";
import CustomDropdown from "../CustomDropdown";

const WalkInVisitForm = ({ fieldsToRender, onAddGuest, onClose }) => {
  const defaultFields = [
    "facility",
    "escortName",
    "startDate",
    "startTime",
    "endTime",
    "visitorName",
    "dob",
    "guestMail",
    "phoneNumber",
    "relationship",
  ];
  
  fieldsToRender = fieldsToRender || defaultFields;
  const durationOptions = ["2:00 Hour", "4:00 Hour", "8:00 Hour", "12:00 Hour"];

  // Set startTime in 24-hour format
  const currentTime = new Date().toLocaleTimeString([], {
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
  });

  const initialValues = {
    facility: "",
    escortName: "",
    startDate: new Date(),
    startTime: currentTime,
    endTime: "4:00 Hour",
    visitorName: "",
    dob: "",
    guestMail: "",
    phoneNumber: "",
    relationship: "",
    image: "",
  };

  const validationSchema = Yup.object({
    visitorName: Yup.string().required("Guest Name is required"),
  });

  const handleSubmit = (values, { resetForm }) => {
    console.log("Form submitted with values:", values);
    
    // Create a new guest object
    const newGuest = {
      visitorName: values.visitorName,
      dob: values.dob,
      guestMail: values.guestMail,
      phoneNumber: values.phoneNumber,
      relationship: values.relationship,
      startDate: `${values.startDate.toLocaleDateString()} ${values.startTime}`,
      endTime: values.endTime,
      image: values.image,
    };
    
    // Pass the new guest to the parent component
    onAddGuest(newGuest);
    
    // Reset form and close
    resetForm();
    onClose();
  };

  return (
    <div className="px-12 py-6 mb-8 border rounded-md shadow-md">
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ setFieldValue, values, touched, errors, handleBlur }) => (
          <Form className="space-y-4">
            <div className="flex justify-between">
              <h1 className="text-xl font-normal text-[#4F2683]">
                Create Walk In Visit
              </h1>
              <button
                type="button"
                className="text-xl text-white rounded-full h-8 w-8 bg-[#4F2683]"
                onClick={onClose}
              >
                &times;
              </button>
            </div>
            
            <div className="bg-gray-100 p-4 rounded-lg border">
              <div className="flex gap-4 flex-wrap">
                {fieldsToRender.includes("facility") && (
                  <div className="w-full md:w-1/4">
                    <Input
                      type="text"
                      label="Facility"
                      name="facility"
                      value={values.facility}
                      onChange={(e) => setFieldValue("facility", e.target.value)}
                      onBlur={handleBlur}
                    />
                  </div>
                )}
                
                {fieldsToRender.includes("escortName") && (
                  <div className="w-full md:w-1/4">
                    <Input
                      type="text"
                      label="Escort Name"
                      name="escortName"
                      value={values.escortName}
                      onChange={(e) => setFieldValue("escortName", e.target.value)}
                      onBlur={handleBlur}
                    />
                  </div>
                )}
                
                {fieldsToRender.includes("startDate") && (
                  <div className="w-full md:w-1/4">
                    <DateInput
                      label="Start Date"
                      name="startDate"
                      value={values.startDate}
                      onChange={(date) => setFieldValue("startDate", date)}
                      onBlur={handleBlur}
                    />
                  </div>
                )}
                
                {fieldsToRender.includes("startTime") && (
                  <div className="w-full md:w-1/4">
                    <Input
                      type="time"
                      label="Start Time"
                      name="startTime"
                      value={values.startTime}
                      onChange={(e) => setFieldValue("startTime", e.target.value)}
                      onBlur={handleBlur}
                    />
                  </div>
                )}
                
                {fieldsToRender.includes("endTime") && (
                  <div className="w-full md:w-1/4">
                    <h2>Duration</h2>
                    <CustomDropdown
                      options={durationOptions}
                      defaultValue="4:00 Hour"
                      onChange={(value) => setFieldValue("endTime", value)}
                    />
                  </div>
                )}
              </div>
            </div>
            
            <div className="p-4">
              <div>
                <ImageCapture
                  onImageCaptured={(img) => setFieldValue("image", img)}
                  onImageUploaded={(img) => setFieldValue("image", img)}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
                {fieldsToRender.includes("visitorName") && (
                  <div className="w-full">
                    <Input
                      type="text"
                      label="Guest Name*"
                      name="visitorName"
                      value={values.visitorName}
                      placeholder="Enter Guest Name"
                      onChange={(e) => setFieldValue("visitorName", e.target.value)}
                      onBlur={handleBlur}
                    />
                    {touched.visitorName && errors.visitorName && (
                      <div className="text-red-500 text-sm">{errors.visitorName}</div>
                    )}
                  </div>
                )}
                
                {fieldsToRender.includes("dob") && (
                  <div className="w-full">
                    <DateInput
                      label="Guest DOB"
                      name="dob"
                      value={values.dob}
                      onChange={(date) => setFieldValue("dob", date)}
                      onBlur={handleBlur}
                    />
                  </div>
                )}
                
                {fieldsToRender.includes("guestMail") && (
                  <div className="w-full">
                    <Input
                      type="email"
                      label="Guest Mail"
                      name="guestMail"
                      value={values.guestMail}
                      placeholder="Enter Guest Email"
                      onChange={(e) => setFieldValue("guestMail", e.target.value)}
                      onBlur={handleBlur}
                    />
                  </div>
                )}
                
                {fieldsToRender.includes("phoneNumber") && (
                  <div className="w-full">
                    <Input
                      type="tel"
                      label="Phone Number"
                      name="phoneNumber"
                      value={values.phoneNumber}
                      placeholder="Enter Phone Number"
                      onChange={(e) => setFieldValue("phoneNumber", e.target.value)}
                      onBlur={handleBlur}
                    />
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex gap-4 justify-center">
              <Button
                type="submit"
                label="Save"
              />
              <Button
                type="button"
                onClick={onClose}
                label="Cancel"
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default WalkInVisitForm;