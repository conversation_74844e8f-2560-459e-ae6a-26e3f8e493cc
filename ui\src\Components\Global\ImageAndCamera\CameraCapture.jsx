import React, { useState, useRef, useEffect } from 'react';

const CameraCapture = ({ captureImage, showCamera, handleImageUpload, handleToggleCamera, imageSrc, demoimg }) => {
  const [hasCameraPermission, setHasCameraPermission] = useState(false);
  const [cameraStream, setCameraStream] = useState(null);
  const videoRef = useRef(null); 
  
  const canvasRef = useRef(null);

  useEffect(() => {
    // Request camera access
    const getCamera = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        setCameraStream(stream);
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
        setHasCameraPermission(true);
      } catch (err) {
        console.error('Error accessing camera:', err);
        setHasCameraPermission(false);
      }
    };

    getCamera();

    return () => {
      if (cameraStream) {
        cameraStream.getTracks().forEach(track => track.stop()); // Stop camera when unmounting
      }
    };
  }, [cameraStream]);

  const handleCaptureImage = () => {
    if (canvasRef.current && videoRef.current) {
      const context = canvasRef.current.getContext('2d');
      context.drawImage(videoRef.current, 0, 0, canvasRef.current.width, canvasRef.current.height);
      const image = canvasRef.current.toDataURL('image/png'); // Get the captured image
      captureImage(image); // Pass captured image to parent
    }
  };

  return (
    <div className="camera-capture-container">
      {!hasCameraPermission ? (
        <p>Camera access is required to capture an image.</p>
      ) : (
        <div className="camera-container">
          <video ref={videoRef} autoPlay width="100%" height="auto" />
          <canvas ref={canvasRef} width="640" height="480" style={{ display: 'none' }} />
          <button onClick={handleCaptureImage} className="capture-button">
            Capture Image
          </button>
        </div>
      )}
    </div>
  );
};

export default CameraCapture;
