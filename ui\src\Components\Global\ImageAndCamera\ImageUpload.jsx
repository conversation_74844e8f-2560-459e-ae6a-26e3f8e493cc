import React, { useRef, useState } from 'react';
import Webcam from 'react-webcam';

const ImageUpload = () => {
  const [imageSrc, setImageSrc] = useState(null);

  // Define webcamRef using useRef
  const webcamRef = useRef(null);

  const captureImage = () => {
    if (webcamRef.current) {
      const imageSrc = webcamRef.current.getScreenshot();
      setImageSrc(imageSrc);
    }
  };

  return (
    <div>
      {/* Webcam component */}
      <Webcam
        audio={false}
        ref={webcamRef}
        screenshotFormat="image/jpeg"
        width="100%"
      />

      {/* Button to capture image */}
      <button onClick={captureImage}>Capture Image</button>

      {/* Display the captured image */}
      {imageSrc && <img src={imageSrc} alt="Captured" />}
    </div>
  );
};

export default ImageUpload;
