import React from "react";

const Input = ({
  type,
  placeholder,
  label,
  onChange,
  options,
  className = "",
  height = "40px",
  error = null,
  passValue = false,
  bubbles = false,
  bubbleOptions = [],
  value,
  ...rest
}) => {
  const handleChange = (e) => {
    if (!onChange) return;
    if (passValue) {
      onChange(e.target.value);
    } else {
      onChange(e);
    }
  };

  // Handle a click on one of the bubble words.
  const handleBubbleClick = (bubble) => {
    // Prepend the bubble text (followed by a space) to the current value.
    const newValue = (value ? value + " " : "") + bubble;

    if (passValue) {
      onChange(newValue);
    } else {
      // Create a synthetic event to pass the new value.
      onChange({ target: { value: newValue, name: rest.name } });
    }
  };

  if (type === "select") {
    return (
      <div className="w-full">
        {label && (
          <label className="block font-normal text-gray-700 mb-1">
            {label}
          </label>
        )}
        <div>
          <select
            className="p-2 border rounded w-full focus:outline-none focus:ring-1"
            onChange={handleChange}
            {...rest}
          >
            {options &&
              options.map((option, idx) => (
                <option key={idx} value={option.value}>
                  {option.label}
                </option>
              ))}
          </select>
          {error && <p className="text-red-500">{error?.message}</p>}
        </div>
      </div>
    );
  }

  if (type === "textarea") {
    return (
      <div className="w-full">
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {label}
          </label>
        )}
        <div>
          <textarea
            placeholder={placeholder}
            className={`p-2 border rounded w-full focus:outline-none focus:ring-1 focus:ring-[#4F2683] ${className}`}
            onChange={handleChange}
            style={{ height }}
            {...rest}
          />
          {error && <p className="text-red-500">{error?.message}</p>}
        </div>
      </div>
    );
  }

  if (type === "bubbles") {
    return (
      <div className="w-full">
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {label}
          </label>
        )}
        <div
          className={`flex relative gap-2 border rounded p-2 focus-within:ring-1 focus-within:ring-[#4F2683] h-[94px] ${className}`}
          style={{ minHeight: height }}
        >
          <textarea
            // type="text"
            placeholder={placeholder}
            value={value}
            onChange={handleChange}
            className="flex w-full border-none outline-none "
            {...rest}
          />
            <div className="absolute flex gap-2 bottom-0 mb-1" >
          {bubbles &&
            bubbleOptions.map((bubble, idx) => (
              <span
                key={idx}
                className=" bg-[#EEE9F2] px-2 py-1 rounded-full text-sm text-[#4F2386] cursor-pointer"
                onClick={() => handleBubbleClick(bubble)}
              >
                {bubble}
              </span>
            ))}
          </div>

          
        </div>
        {error && <p className="text-red-500">{error?.message}</p>}
      </div>
    );
  }

  // Standard input for all other types.
  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
      <div>
        <input
          type={type}
          placeholder={placeholder}
          value={value}
          className={`p-2 border rounded w-full focus:outline-none focus:ring-1 focus:ring-[#4F2683] ${className}`}
          onChange={handleChange}
          style={{ height }}
          {...rest}
        />
        {error && <p className="text-red-500">{error?.message}</p>}
      </div>
    </div>
  );
};

export default Input;
