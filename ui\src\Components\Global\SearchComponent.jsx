// import React from "react";
// import SearchBar from "./SearchBar";
// import searchIcon from "../Images/search-icon.png";
// import HomeLogo from "../Images/HomeLogo.svg";

// const SearchComponent = () => {
//   const handleSearch = () => {
//     alert("Search button clicked!");
//   };

//   return (
//     <div className="flex flex-col items-center gap-4 p-4 w-full max-w-screen-2xl mx-auto overflow-x-hidden">
//       {/* Search Bars Container */}
//       <div className="flex items-center gap-1 h-12">
//         {/* Home Link */}
//         <div className="border rounded-md bg-gray-200 h-full flex items-center px-2">
//           <img src={HomeLogo} alt="Home Logo" className="h-full" />
//         </div>

//         {/* First Search Bar */}
//         <SearchBar
//           placeholder="Search here..."
//           iconSrc={searchIcon}
//           onSearch={handleSearch}
//           wrapperClassName="shadow-lg h-full"
//           borderColor="#4F2683"
//         />
//         {/* Second Search Bar */}
//         <SearchBar
//           placeholder="Search for items..."
//           iconSrc={searchIcon}
//           onSearch={() => console.log("Second search clicked!")}
//           wrapperClassName="shadow-md h-full"
//           borderColor="#4F2683"
//         />
//       </div>
//     </div>
//   );
// };

// export default SearchComponent;
