import React, { useState, useEffect } from "react";
import { defaultIdentities } from "../../api/static";

const SearchableDropdown = ({ value, onSelect }) => {
  const [searchTerm, setSearchTerm] = useState(value || "");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    // Update local state when external value changes.
    setSearchTerm(value || "");
  }, [value]);

  const filteredIdentities = defaultIdentities.filter((identity) =>
    `${identity.name} ${identity.eid} ${identity.type}`
      .toLowerCase()
      .includes(searchTerm.toLowerCase())
  );

  return (
    <div className="relative w-full">
      <input
        type="text"
        placeholder="Search Identity"
        className="w-full p-2 border rounded-md focus:outline-none"
        value={searchTerm}
        onChange={(e) => {
          setSearchTerm(e.target.value);
          setIsDropdownOpen(true);
        }}
        onFocus={() => setIsDropdownOpen(true)}
      />
      {isDropdownOpen && searchTerm && (
        <ul className="absolute w-full mt-1 bg-white border rounded-md shadow-lg max-h-40 overflow-auto z-10">
          {filteredIdentities.length > 0 ? (
            filteredIdentities.map((identity) => (
              <li
                key={identity.eid}
                className="p-2 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  setSearchTerm(identity.name);
                  setIsDropdownOpen(false);
                  if (onSelect) {
                    onSelect(identity);
                  }
                }}
              >
                {identity.name} ({identity.eid}) - {identity.type}
              </li>
            ))
          ) : (
            <li className="p-2 text-gray-500">No results found</li>
          )}
        </ul>
      )}
    </div>
  );
};

export default SearchableDropdown;
