import React, { useState } from "react";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";
import DateInput from "../../Global/Input/DateInput";

const ViewEditCardForm = ({ cardData, onUpdate, onClose }) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState({
    id: cardData.id || "",
    Cnumber: cardData.Cnumber || "",
    Cformat: cardData.Cformat || "",
    facilityCode: cardData.facilityCode || "",
    pin: cardData.pin || "",
    Template: cardData.Template || "",
    activation: cardData.activation || "",
    deactivation: cardData.deactivation || "",
    status: cardData.status || "",
    reason: cardData.reason || "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };
  
  const handleDateChange = (name, value) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value, // Directly setting the selected date value
    }));
  };
  
  const handleSave = (e) => {
    e.preventDefault();
    onUpdate(formData);
    setIsEditMode(false);
  };

  const inputClassName = `w-full border bg-transparent rounded p-2 ${
    isEditMode ? "focus:outline-none" : "border-none text-[#8F8F8F]"
  }`;

  return (
    <div className="w-full p-2">
      <div className="flex items-center mb-2 px-2 justify-between">
        <h2 className="text-[30px] font-normal text-[#4F2683]">Card Details</h2>
        <button
          className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
          type="button"
          onClick={onClose}
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />
      <form onSubmit={handleSave} className="bg-white p-6 rounded-lg">
        {/* Card Number */}
        <div className="flex items-center mb-4">
          <label htmlFor="Cnumber" className="w-1/4 text-[16px] font-normal">
            Card Number
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="Cnumber"
              id="Cnumber"
              value={formData.Cnumber}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        {/* Card Format */}
        <div className="flex items-center mb-4">
          <label htmlFor="Cformat" className="w-1/4 text-[16px] font-normal">
            Card Format
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="Cformat"
              id="Cformat"
              value={formData.Cformat}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        {/* Facility Code */}
        <div className="flex items-center mb-4">
          <label htmlFor="facilityCode" className="w-1/4 text-[16px] font-normal">
            Facility Code
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="facilityCode"
              id="facilityCode"
              value={formData.facilityCode}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        {/* Pin */}
        <div className="flex items-center mb-4">
          <label htmlFor="pin" className="w-1/4 text-[16px] font-normal">
            Pin
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="pin"
              id="pin"
              value={formData.pin}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        {/* Template */}
        <div className="flex items-center mb-4">
          <label htmlFor="Template" className="w-1/4 text-[16px] font-normal">
            Template
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="Template"
              id="Template"
              value={formData.Template}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        {/* Activation Date */}
        <div className="flex items-center mb-4">
          <label htmlFor="activation" className="w-1/4 text-[16px] font-normal">
            Activation Date
          </label>
          <div className="w-3/4">
          {isEditMode ? (
            <DateInput
            name="activation"
            id="activation"
            value={formData.activation}
            onChange={(date) => handleDateChange("activation", date)}
            placeholder="MM-DD-YYYY"
          />
          ):(

            <Input
              type="text"
              name="activation"
              id="activation"
              value={formData.deactivation ? new Date(formData.deactivation).toISOString().split("T")[0] : ""}
              onChange={(e) => handleChange({ target: { name: "deactivation", value: e.target.value } })}
              disabled={isEditMode}
              className={inputClassName}
            />
            
          )}

          </div>
        </div>

        {/* Deactivation Date */}
        <div className="flex items-center mb-4">
          <label htmlFor="deactivation" className="w-1/4 text-[16px] font-normal">
            Deactivation Date
          </label>
          <div className="w-3/4">
          {isEditMode ? (
            <DateInput
            name="deactivation"
            id="deactivation"
            value={formData.deactivation}
            onChange={(date) => handleDateChange("deactivation", date)}
            placeholder="MM-DD-YYYY"
          />
          ):(

            <Input
              type="text"
              name="deactivation"
              id="deactivation"
              value={formData.deactivation}
              onChange={handleChange}
              disabled={isEditMode}
              className={inputClassName}
            />
          )}
          </div>
        </div>

        {/* Status */}
        <div className="flex items-center mb-4">
          <label htmlFor="status" className="w-1/4 text-[16px] font-normal">
            Status
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <CustomDropdown
                className="h-11 rounded border-gray-300"
                options={["Active", "Inactive", "Expired"]}
                onSelect={(option) =>
                  setFormData({ ...formData, status: option })
                }
                selectedOption={formData.status}
                value={formData.status}
                hoverBgColor="hover:bg-[#4F2683]"
              />
            ) : (
              <Input
                type="text"
                name="status"
                id="status"
                value={formData.status}
                disabled
                className={inputClassName}
              />
            )}
          </div>
        </div>

        {/* Reason */}
        <div className="flex items-center mb-4">
          <label htmlFor="reason" className="w-1/4 text-[16px] font-normal">
            Reason
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="reason"
              id="reason"
              value={formData.reason}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        <div className="flex gap-4 justify-end">
          {!isEditMode ? (
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                setIsEditMode(true);
              }}
              className="px-4 py-2 bg-[#4F2683] text-white rounded"
            >
              Edit
            </button>
          ) : (
            <>
              <button
                type="button"
                onClick={() => {
                  setIsEditMode(false);
                  setFormData({
                    id: cardData.id || "",
                    Cnumber: cardData.Cnumber || "",
                    Cformat: cardData.Cformat || "",
                    facilityCode: cardData.facilityCode || "",
                    pin: cardData.pin || "",
                    Template: cardData.Template || "",
                    activation: cardData.activation || "",
                    deactivation: cardData.deactivation || "",
                    status: cardData.status || "",
                    reason: cardData.reason || "",
                  });
                }}
                className="px-4 py-2 bg-gray-400 text-white rounded"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-[#4F2683] text-white rounded"
              >
                Save
              </button>
            </>
          )}
        </div>
      </form>
    </div>
  );
};

export default ViewEditCardForm;
