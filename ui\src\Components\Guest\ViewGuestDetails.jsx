import React, { useState } from "react";

const ViewGuestDetails = ({ guest, onClose }) => {
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  return (
   <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Guest Details</h2>
           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        <div className="p-6">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">
            Guest Information
          </h2>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              First Name
            </label>
            <div className="w-3/4">
              <p className="p-2 border h-11 rounded bg-gray-100">
                {guest.firstName || guest.name?.split(" ")[0] || "N/A"}
              </p>
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Last Name
            </label>
            <div className="w-3/4">
              <p className="p-2 border h-11 rounded bg-gray-100">
                {guest.lastName || guest.name?.split(" ").slice(1).join(" ") || "N/A"}
              </p>
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Email
            </label>
            <div className="w-3/4">
              <p className="p-2 border h-11 rounded bg-gray-100">{guest.email}</p>
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Mobile Phone
            </label>
            <div className="w-3/4">
              <p className="p-2 border h-11 rounded bg-gray-100">
                {guest.mobilePhone || "N/A"}
              </p>
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Company
            </label>
            <div className="w-3/4">
              <p className="p-2 border h-11 rounded bg-gray-100">
                {guest.company || "N/A"}
              </p>
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Private Visitor?
            </label>
            <div className="w-3/4">
              <p className="p-2 border h-11 rounded bg-gray-100">
                {guest.isPrivate}
              </p>
            </div>
          </div>
          <div className="flex justify-center gap-4 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-400 text-white rounded"
            >
              Close
            </button>
          </div>
        </div>
        </div>
        </div>
      </div>
    </div>
  );
};

export default ViewGuestDetails;