import React from "react";
import { IoClose } from "react-icons/io5";

const faqData = [
	{
		question: "Automated workflows that reduce manual guest processing ?",
		answer: "Automated workflows streamline guest processing, reducing manual effort and errors.",
	},
	{
		question: "Real-time visibility and alerts for enhanced security and response.",
		answer: "Get instant notifications and visibility to respond quickly to security events.",
	},
	{
		question: "Intuitive interfaces requiring minimal training.",
		answer: "Our interfaces are designed for ease of use, minimizing the need for training.",
	},
	{
		question: "Streamlined compliance and reporting, saving valuable time.",
		answer: "Automated compliance and reporting tools save time and ensure accuracy.",
	},
];

const Help = ({ isOpen, onClose }) => {
	const [openIndexes, setOpenIndexes] = React.useState([]);
	const [show, setShow] = React.useState(false);

	const toggleIndex = (idx) => {
		setOpenIndexes((prev) =>
			prev.includes(idx) ? prev.filter((i) => i !== idx) : [...prev, idx]
		);
	};

	React.useEffect(() => {
		if (isOpen) {
			const timer = setTimeout(() => setShow(true), 10);
			return () => clearTimeout(timer);
		} else {
			setShow(false);
		}
	}, [isOpen]);

	const handleClose = () => {
		setShow(false);
		setTimeout(onClose, 700);
	};

	return (
		<div
			className={`fixed top-0 left-0 h-full w-full bg-gray-900 bg-opacity-50 z-50 ${isOpen ? "block" : "hidden"
				}`}
		>
			<div
				className={`fixed top-0 right-0 h-full w-[700px] max-w-full rounded-l-md shadow-[0px_8px_14px_0px_#00000036] bg-white z-50 flex flex-col transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"
					}`}
				style={{ willChange: "transform" }}
			>
				<div className="flex justify-between items-center p-6 border-b">
					<h2 className="text-[24px] font-semibold text-[#4F2386]">
						Support/<span className="font-normal">Help</span>
					</h2>
					<div
						className="bg-[#4F2683] rounded-full cursor-pointer p-1"
						onClick={handleClose}
					>
						<IoClose className="text-white text-xl" />
					</div>
				</div>
				<div className="p-6 flex-1 overflow-y-auto bg-[#F5F5F7]">
					<p className="mb-6 text-[#7C7C7C]">
						Your staff are the heart of healthcare. StellarCare lightens their
						load, enhances their control, and allows them to focus on what truly
						matters: patient care.
					</p>
					<ul className="mb-8 space-y-2">
						<li className="flex items-start gap-2">
							<span className="text-[#4F2683] text-xl">✓</span>
							<span>
								Automated workflows that reduce manual guest processing.
							</span>
						</li>
						<li className="flex items-start gap-2">
							<span className="text-[#4F2683] text-xl">✓</span>
							<span>
								Real-time visibility and alerts for enhanced security and
								response.
							</span>
						</li>
						<li className="flex items-start gap-2">
							<span className="text-[#4F2683] text-xl">✓</span>
							<span>Intuitive interfaces requiring minimal training.</span>
						</li>
						<li className="flex items-start gap-2">
							<span className="text-[#4F2683] text-xl">✓</span>
							<span>
								Streamlined compliance and reporting, saving valuable time.
							</span>
						</li>
					</ul>
					<div>
						<h3 className="text-[20px] font-semibold text-[#4F2683] mb-4">
							FAQ{" "}
							<span className="font-normal">
								(Frequently Asked Questions)
							</span>
						</h3>
						<div className="space-y-3">
							{faqData.map((faq, idx) => (
								<div key={idx} className="border rounded-lg bg-white">
									<button
										className="w-full text-left px-4 py-3 focus:outline-none flex justify-between items-center"
										onClick={() => toggleIndex(idx)}
									>
										<span>{faq.question}</span>
										<span className="ml-2">
											{openIndexes.includes(idx) ? "▲" : "▼"}
										</span>
									</button>
									{openIndexes.includes(idx) && (
										<div className="px-4 pb-3 text-[#7C7C7C]">
											{faq.answer}
										</div>
									)}
								</div>
							))}
						</div>
					</div>
				</div>
				<div className="flex justify-end gap-3 p-6 border-t">
					<button
						className="bg-gray-200 text-[#4F2683] px-6 py-2 rounded-lg"
						onClick={handleClose}
					>
						Cancel
					</button>
					<button className="bg-[#4F2683] text-white px-6 py-2 rounded-lg">
						Contact Support
					</button>
				</div>
			</div>
		</div>
	);
};

export default Help;

