import React, { forwardRef } from 'react';
import { Rect, Text, Group, Line } from 'react-konva';
import { useSelector } from 'react-redux';
import Mustache from 'mustache';

const BarcodeElement = forwardRef(({
  element,
  isPreviewMode,
  onClick,
  onDragEnd,
}, ref) => {
  const { mockData } = useSelector((state) => state.idDesigner);

  const getBarcodeData = () => {
    if (isPreviewMode && element.data) {
      try {
        return Mustache.render(element.data, mockData);
      } catch (error) {
        console.warn('Error rendering barcode data template:', error);
        return element.data;
      }
    }
    return element.data || 'Barcode Data';
  };

  const renderBarcodeLines = () => {
    const lines = [];
    const lineCount = 20;
    const lineWidth = element.width / lineCount;
    const barcodeHeight = element.height * 0.65; // Leave space for text
    const startY = element.height * 0.05; // Small top margin

    for (let i = 0; i < lineCount; i++) {
      const x = i * lineWidth + (lineWidth * 0.1); // Small left margin
      const isWide = i % 3 === 0;
      const actualLineWidth = isWide ? lineWidth * 0.6 : lineWidth * 0.3;

      lines.push(
        <Line
          key={i}
          points={[x, startY, x, startY + barcodeHeight]}
          stroke="#000"
          strokeWidth={actualLineWidth}
        />
      );
    }
    return lines;
  };

  return (
    <Group
      ref={ref}
      x={element.x}
      y={element.y}
      offsetX={0}
      offsetY={0}
      width={element.width}
      height={element.height}
      rotation={element.rotation || 0}
      opacity={element.opacity !== undefined ? element.opacity : 1}
      visible={element.visible !== false}
      draggable={!element.locked && !isPreviewMode}
      onClick={onClick}
      onDragEnd={onDragEnd}
    >
      {/* Background */}
      <Rect
        x={0}
        y={0}
        width={element.width}
        height={element.height}
        fill="#ffffff"
        stroke="#ccc"
        strokeWidth={1}
      />

      {/* Barcode lines */}
      <Group x={0} y={0}>
        {renderBarcodeLines()}
      </Group>

      {/* Data text */}
      <Text
        x={element.width * 0.05}
        y={element.height * 0.75}
        width={element.width * 0.9}
        height={element.height * 0.2}
        text={getBarcodeData()}
        fontSize={Math.min(12, element.height * 0.12)}
        fill="#000"
        align="center"
        verticalAlign="middle"
        fontFamily="monospace"
      />
    </Group>
  );
});

BarcodeElement.displayName = 'BarcodeElement';

export default BarcodeElement;
