import React, { useState, useEffect } from "react";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";
import DateInput from "../../Global/Input/DateInput";
import SearchBar from "../../Global/SearchBar";
import {
  getIdentityAccessDetails,
  updateIdentityAccess,
  getAccessLevel,
  getCardNumber,
} from "../../../api/identity";
import { toast } from "react-toastify";
import { useIdentityData } from "../../../hooks/useIdentityData";

const ViewEditAccessArea = ({ accessData, onUpdate, onClose }) => {
  const { accessStatusOptions, accessStatusMap } = useIdentityData();
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState({
    identity_access_id: "",
    access_level_id: "",
    card_id: "",
    startDate: "",
    endDate: "",
    status: "",
  });
  const [accessOptions, setAccessOptions] = useState([]);
  const [cardOptions, setCardOptions] = useState([]);
  const [accessAreaName, setAccessAreaName] = useState("");
  const [cardNumber, setCardNumber] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [show, setShow] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // Sync form data when accessData changes (if provided as prop)
  useEffect(() => {
    if (accessData) {
      setFormData({
        identity_access_id: accessData.identity_access_id ?? "",
        access_level_id: accessData.access_level_id ?? "",
        card_id: accessData.card_id ?? "",
        startDate: accessData.start_date ?? "",
        endDate: accessData.end_date ?? "",
        status: accessData.status ?? "",
      });
      setAccessAreaName(accessData.access_level_name || accessData.areaName || "");
      setCardNumber(accessData.card_number || accessData.cardNumber || "");
    }
  }, [accessData]);

  // Fetch card numbers for dropdown
  useEffect(() => {
    const fetchCardNumbers = async () => {
      try {
        const response = await getCardNumber();
        setCardOptions(response?.data || []);
      } catch (error) {
        setCardOptions([]);
      }
    };
    fetchCardNumbers();
  }, []);

  // Fetch access area options for dropdown/search
  useEffect(() => {
    const fetchAccessLevels = async () => {
      try {
        const result = await getAccessLevel();
        console.log("Access levels:", result);
        setAccessOptions(result || []);
      } catch (err) {
        setAccessOptions([]);
      }
    };
    fetchAccessLevels();
  }, []);

  // Access area search
  const handleAccessSearch = async (input) => {
    setAccessAreaName(input);
    setShowSuggestions(true);
    try {
      const result = await getAccessLevel({ search: input });
      setAccessOptions(result);
    } catch (err) {
      setAccessOptions([]);
    }
  };

  const handleAccessSelect = (selected) => {
     console.log("Selected access level:", selected);
    setAccessAreaName(selected.name);
    setFormData((prev) => ({ ...prev, access_level_id: selected.id }));
    setShowSuggestions(false);
  };

  const handleCardSelect = (selectedId) => {
    setFormData((prev) => ({ ...prev, card_id: selectedId }));
    const matched = cardOptions.find((c) => c.id === selectedId);
    setCardNumber(matched ? matched.card_number : "");
  };

  const handleDateChange = (name, date) => {
    const dateValue = date ? date.toISOString().split("T")[0] : "";
    setFormData((prev) => ({ ...prev, [name]: dateValue }));
  };

  const handleStatusChange = (val) => {
    setFormData((prev) => ({ ...prev, status: val }));
  };

  const handleSave = async (e) => {
    e.preventDefault();

    
  if (!formData.identity_access_id) {
    toast.error("Invalid access ID. Cannot update.");
    return;
  }
    try {
      const payload = {
        access_level_id: formData.access_level_id,
        card_id: formData.card_id,
        start_date: formData.startDate,
        end_date: formData.endDate,
        status:
          typeof formData.status === "object"
            ? formData.status.value
            : formData.status,
      };
      console.log("Payload for update:", payload);
      await updateIdentityAccess(formData.identity_access_id, payload);
      toast.success("Access area updated successfully!");
      // Pass updated values to parent
      onUpdate({
        ...formData,
        areaName: accessAreaName,
        cardNumber,
        status:
          accessStatusMap && accessStatusMap[formData.status]
            ? accessStatusMap[formData.status]
            : formData.status,
      });
      setIsEditMode(false);
    } catch (error) {
      toast.error("Failed to update access area.");
    }
  };

  const inputClassName = `w-full bg-transparent rounded ${
    isEditMode ? "focus:outline-none" : "border-none text-[#8F8F8F]"
  }`;

  return (
     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
           <div
  className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
  style={{ willChange: "transform" }}
>

        {/* ...existing code... */}
       <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
  <h2 className="text-[24px] md:text-[30px] font-normal text-[#4F2683]">
    Access Area Details
  </h2>
  <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
</div>

       <div className="p-6">
  <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">
    <form onSubmit={handleSave} className="p-6">

          {/* Access Area */}
          <div className="flex items-center mb-4">
            <label htmlFor="accessArea" className="w-1/4 text-[16px] font-normal">
              Access Area
            </label>
            <div className="w-3/4">
              {isEditMode ? (
                <>
                  <SearchBar
                    placeholder="Search Access Area"
                    value={accessAreaName}
                    onInputChange={handleAccessSearch}
                    onClick={() => setShowSuggestions(true)}
                  />
                  {showSuggestions && accessOptions.length > 0 && (
                    <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded mt-1 max-h-48 overflow-y-auto shadow-md">
                      {accessOptions
                      .map((area) => (
                        <li
                          key={area.id}
                          className="px-3 py-2 hover:bg-[#4F2683] hover:text-white cursor-pointer"
                          onClick={() => handleAccessSelect(area)}
                        >
                          {area.name}
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              ) : (
                <Input
                  type="text"
                  name="accessArea"
                  id="accessArea"
                  value={accessAreaName}
                  disabled
                  className={inputClassName}
                />
              )}
            </div>
          </div>

          {/* Card Number */}
          <div className="flex items-center mb-4">
            <label htmlFor="cardNumber" className="w-1/4 text-[16px] font-normal">
              Card Number
            </label>
            <div className="w-3/4">
              {isEditMode ? (
                <CustomDropdown
                  className="h-11 rounded"
                   options={
                  Array.isArray(cardOptions)
                    ? cardOptions
                        .filter(
                          (card) =>
                            card &&
                            typeof card === "object" &&
                            card.id !== undefined &&
                            card.id !== null &&
                            card.card_number !== undefined &&
                            card.card_number !== null
                        )
                        .map((card) => ({
                          label: card.card_number,
                          value: card.id,
                        }))
                    : []
                }
                  placeholder="Card Number"
                  onSelect={handleCardSelect}
                  selectedOption={formData.card_id}
                  value={formData.card_id}
                  hoverBgColor="hover:bg-[#4F2683]"
                  borderColor="border-gray-300"
                />
              ) : (
                <Input
                  type="text"
                  name="cardNumber"
                  id="cardNumber"
                  value={cardNumber}
                  disabled
                  className={inputClassName}
                />
              )}
            </div>
          </div>

          {/* Start Date */}
          <div className="flex items-center mb-4">
            <label htmlFor="startDate" className="w-1/4 text-[16px] font-normal">
              Start Date
            </label>
            <div className="w-3/4">
              {isEditMode ? (
                <DateInput
                  value={formData.startDate}
                  onChange={(date) => handleDateChange("startDate", date)}
                  placeholder="MM-DD-YYYY"
                  className={inputClassName}
                />
              ) : (
                <Input
                  type="text"
                  name="startDate"
                  id="startDate"
                  value={formData.startDate}
                  disabled
                  className={inputClassName}
                />
              )}
            </div>
          </div>

          {/* End Date */}
          <div className="flex items-center mb-4">
            <label htmlFor="endDate" className="w-1/4 text-[16px] font-normal">
              End Date
            </label>
            <div className="w-3/4">
              {isEditMode ? (
                <DateInput
                  value={formData.endDate}
                  onChange={(date) => handleDateChange("endDate", date)}
                  placeholder="MM-DD-YYYY"
                  className={inputClassName}
                />
              ) : (
                <Input
                  type="text"
                  name="endDate"
                  id="endDate"
                  value={formData.endDate}
                  disabled
                  className={inputClassName}
                />
              )}
            </div>
          </div>

          {/* Status */}
          <div className="flex items-center mb-4">
            <label htmlFor="status" className="w-1/4 text-[16px] font-normal">
              Status
            </label>
            <div className="w-3/4">
              {isEditMode ? (
                <CustomDropdown
                  className="h-11 rounded border-gray-300"
                  options={accessStatusOptions}
                  onSelect={handleStatusChange}
                  selectedOption={formData.status}
                  value={formData.status}
                  hoverBgColor="hover:bg-[#4F2683]"
                />
              ) : (
                <Input
                  type="text"
                  name="status"
                  id="status"
                  value={
                    accessStatusMap && accessStatusMap[formData.status]
                      ? accessStatusMap[formData.status]
                      : formData.status
                  }
                  disabled
                  className={inputClassName}
                />
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 justify-end mt-8">
            {!isEditMode ? (
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  setIsEditMode(true);
                }}
                className="px-6 py-2 bg-[#4F2683] text-white rounded hover:bg-[#3c1d64] transition-colors"
              >
                Edit
              </button>
            ) : (
              <>
                <button
                  type="button"
                  onClick={() => {
                    setIsEditMode(false);
                    // Reset form data to the original accessData
                    setFormData({
                      identity_access_id: accessData?.identity_access_id || "",
                      access_level_id: accessData?.access_level_id || "",
                      card_id: accessData?.card_id || "",
                      startDate: accessData?.start_date || "",
                      endDate: accessData?.end_date || "",
                      status: accessData?.status || "",
                    });
                    setAccessAreaName(accessData?.access_level_name || accessData?.areaName || "");
                    setCardNumber(accessData?.card_number || accessData?.cardNumber || "");
                  }}
                  className="px-6 py-2 bg-gray-400 text-white rounded hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-[#4F2683] text-white rounded hover:bg-[#3c1d64] transition-colors"
                >
                  Save Changes
                </button>
              </>
            )}
          </div>
        </form>
        </div>
        </div>
      </div>
    </div>
  );
};

export default ViewEditAccessArea;
