import React, { useState } from "react";
import Button from "../../Global/Button";
import CustomDropdown from "../../Global/CustomDropdown";
import Input from "../../Global/Input/Input"; // Importing the Input component

const AddBuildingForm = ({ onSubmit, onClose }) => {
  const [facility, setFacility] = useState("");
  const [building, setBuilding] = useState("");
  const [buildingCode, setBuildingCode] = useState("");
  const [address, setAddress] = useState("");
  const [city, setCity] = useState("Chennai");
  const [status, setStatus] = useState("");
  const [type, setType] = useState("");
  const [occupancyType, setOccupancyType] = useState("");
  const [buildingPhone, setBuildingPhone] = useState("");
  const [facilityEmail, setFacilityEmail] = useState("");
  const [geoLocationCode, setGeoLocationCode] = useState("");
  const [otherCode, setOtherCode] = useState("");
  const [buildingUrl, setBuildingUrl] = useState("");
  const [connectedApplication, setConnectedApplication] = useState("");
  const [facilityNotes, setFacilityNotes] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    const newBuilding = {
      facility,
      building,
      buildingCode,
      address,
      city,
      status,
      type,
      occupancyType,
      buildingPhone,
      facilityEmail,
      geoLocationCode,
      otherCode,
      buildingUrl,
      connectedApplication,
      facilityNotes,
    };
    onSubmit(newBuilding);
  };

  return (
    <div className="w-full p-0">
      <div className="flex items-center mb-2 px-4 pt-2 justify-between">
        <h2 className="text-[30px] font-normal text-[#4F2683]">Add Building</h2>
        <button
          className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
          type="button"
          onClick={onClose}
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />
      <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-lg">
        <h2 className="text-[20px] text-[#333333] font-medium pb-4">
          Building Details
        </h2>
        {/* Facility */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="facility"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Facility*
          </label>
          <div className="w-3/4">
            <CustomDropdown
              className="h-11 rounded"
              placeholder="Select Facility"
              options={["Facility 1", "Facility 2", "Facility 3"]}
              onSelect={(option) => setFacility(option)}
              selectedOption={facility}
              value={facility}
              bgColor="bg-[white] text-black"
              textColor="text-black"

              hoverBgColor="hover:bg-[#4F2683]"
              borderColor="border-gray-300"
            />
          </div>
        </div>

        {/* Building */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="building"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Building*
          </label>
          <div className="w-3/4">
            <CustomDropdown
              className="h-11 rounded"
              placeholder="Select Building"
              options={["Building A", "Building B", "Building C"]}
              onSelect={(option) => setBuilding(option)}
              selectedOption={building}
              value={building}
              bgColor="bg-[white] text-black"
              textColor="text-black"

              hoverBgColor="hover:bg-[#4F2683]"
              borderColor="border-gray-300"
            />
          </div>
        </div>

        <div className="flex items-center mb-4">
          <label
            htmlFor="buildingCode"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Building Code
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              id="buildingCode"
              placeholder="Building Code"
              value={buildingCode}
              onChange={(e) => setBuildingCode(e.target.value)}
              required
              className="border-gray-300"
            />
          </div>
        </div>

        <div className="flex items-center mb-4">
          <label
            htmlFor="status"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Status
          </label>
          <div className="w-3/4">
            <CustomDropdown
              className="h-11 rounded"
              options={["Active", "Expired"]}
              onSelect={(option) => setStatus(option)}
              selectedOption={status}
              value={status}
              placeholder="Select Status"
              bgColor="bg-[white] text-black"
              textColor="text-black"
              hoverBgColor="hover:bg-[#4F2683]"
              borderColor="border-gray-300"
            />
          </div>
        </div>

        <div className="flex items-center mb-4">
          <label
            htmlFor="type"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Type
          </label>
          <div className="w-3/4">
            <CustomDropdown
              className="h-11 rounded"
              options={["Type 1", "Type 2"]}
              onSelect={(option) => setType(option)}
              selectedOption={type}
              value={type}
              placeholder="Select Type"
              bgColor="bg-[white] text-black"
              textColor="text-black"
              hoverBgColor="hover:bg-[#4F2683]"
              borderColor="border-gray-300"
            />
          </div>
        </div>

        {/* Occupancy Type */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="occupancyType"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Occupancy Type
          </label>
          <div className="w-3/4">
            <CustomDropdown
              className="h-11 rounded"
              options={["Occupancy Type 1", "Occupancy Type 2"]}
              onSelect={(option) => setOccupancyType(option)}
              selectedOption={occupancyType}
              value={occupancyType}
              placeholder="Occupancy Type"
              bgColor="bg-[white] text-black"
              textColor="text-black"
              hoverBgColor="hover:bg-[#4F2683]"
              borderColor="border-gray-300"
            />
          </div>
        </div>

        {/* Building Phone */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="buildingPhone"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Building Phone
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              id="buildingPhone"
              placeholder="Facility Phone"
              value={buildingPhone}
              onChange={(e) => setBuildingPhone(e.target.value)}
              className="border-gray-300"
            />
          </div>
        </div>

        {/* Facility Email */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="facilityEmail"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Facility Email
          </label>
          <div className="w-3/4">
            <Input
              type="email"
              id="facilityEmail"
              placeholder="Facility Email"
              value={facilityEmail}
              onChange={(e) => setFacilityEmail(e.target.value)}
              className="border-gray-300"
            />
          </div>
        </div>

        {/* Geo Location Code */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="geoLocationCode"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Geo Location Code
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              id="geoLocationCode"
              placeholder="Geo Location Code"
              value={geoLocationCode}
              onChange={(e) => setGeoLocationCode(e.target.value)}
              className="border-gray-300"
            />
          </div>
        </div>

        {/* Other Code */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="otherCode"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Other Code
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              id="otherCode"
              placeholder="Other Code"
              value={otherCode}
              onChange={(e) => setOtherCode(e.target.value)}
              className="border-gray-300"
            />
          </div>
        </div>

        {/* Building URL */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="buildingUrl"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Building URL
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              id="buildingUrl"
              placeholder="Building Url"
              value={buildingUrl}
              onChange={(e) => setBuildingUrl(e.target.value)}
              className="border-gray-300"
            />
          </div>
        </div>

        {/* Connected Application */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="connectedApplication"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Connected Application
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              id="connectedApplication"
              placeholder="Managed by System(s)"
              value={connectedApplication}
              onChange={(e) => setConnectedApplication(e.target.value)}
              className="border-gray-300"
            />
          </div>
        </div>

        {/* Facility Notes */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="facilityNotes"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Building Notes
          </label>
          <div className="w-3/4">
            <Input
              type="textarea"
              id="facilityNotes"
              placeholder="Building Notes"
              value={facilityNotes}
              onChange={(e) => setFacilityNotes(e.target.value)}
              className="border-gray-300"
            />
          </div>
        </div>

        <h2 className="text-[20px] pt-2 text-[#333333] font-medium pb-4">
          Address
        </h2>
        {/* Address */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="address"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Address
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              id="address"
              placeholder="Address"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              required
              className="border-gray-300"
            />
          </div>
        </div>

        <div className="flex gap-4 pb-4 justify-center">
          <Button type="cancel" label="Cancel" onClick={onClose} />
          <Button type="primary" label="Add" />
        </div>
      </form>
    </div>
  );
};

export default AddBuildingForm;
