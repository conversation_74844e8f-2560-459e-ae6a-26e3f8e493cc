import React, { useState } from "react";
import GenericTable from "../../GenericTable";
import AddBuildingForm from "./AddBuildingForm";
import ViewEditBuildingForm from "./ViewEditBuildingForm";


const Building = () => {
  // Initial sample data with all fields
  const [data, setData] = useState([
    {
      id: 1,
      facility: "Facility 1",
      building: "Building A",
      buildingCode: "1234",
      address: "Tech Park",
      city: "Chennai",
      status: "Active",
      type: "Office",
      occupancyType: "Full",
      buildingPhone: "1234567890",
      facilityEmail: "<EMAIL>",
      geoLocationCode: "GEO123",
      otherCode: "OTH123",
      buildingUrl: "https://example.com/1",
      connectedApplication: "System A",
      facilityNotes: "Notes for Building 1",
    },
    {
      id: 2,
      facility: "Facility 2",
      building: "Building B",
      buildingCode: "ABC",
      address: "Tech Park",
      city: "Chennai",
      status: "Active",
      type: "Warehouse",
      occupancyType: "Partial",
      buildingPhone: "9876543210",
      facilityEmail: "<EMAIL>",
      geoLocationCode: "GEO456",
      otherCode: "OTH456",
      buildingUrl: "https://example.com/2",
      connectedApplication: "System B",
      facilityNotes: "Notes for Building 2",
    },
    {
      id: 3,
      facility: "Facility 3",
      building: "Building C",
      buildingCode: "XYZ",
      address: "Somewhere",
      city: "Mumbai",
      status: "Expired",
      type: "Retail",
      occupancyType: "None",
      buildingPhone: "5556667777",
      facilityEmail: "<EMAIL>",
      geoLocationCode: "GEO789",
      otherCode: "OTH789",
      buildingUrl: "https://example.com/3",
      connectedApplication: "System C",
      facilityNotes: "Notes for Building 3",
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [showAddBuildingForm, setShowAddBuildingForm] = useState(false);
  const [showViewBuildingForm, setShowViewBuildingForm] = useState(false);
  const [selectedBuilding, setSelectedBuilding] = useState(null);

  // Table columns: only show a subset of fields
  const columns = [
    {
      name: "Building",
      selector: (row) => row.building,
      cell: (row) => (
        <span
          style={{ textDecoration: "underline", cursor: "pointer" }}
          onClick={() => handleView(row)}
        >
          {row.building}
        </span>
      ),
      sortable: true,
    },
    {
      name: "Building Code",
      selector: (row) => row.buildingCode,
      sortable: true,
    },
    {
      name: "Address",
      selector: (row) => row.address,
      sortable: true,
    },
    {
      name: "City",
      selector: (row) => row.city,
      sortable: true,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center text-sm font-semibold rounded-full ${row.status === "Active"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
            }`}
        >
          {row.status}
        </span>
      ),
      sortable: true,
    },
  ];

  const handleAdd = () => {
    setShowAddBuildingForm(true);
  };
  const handleCloseModal = () => {
    setShowAddBuildingForm(false);
  };

  const handleView = (row) => {
    setSelectedBuilding(row);
    setShowViewBuildingForm(true);
  };
  const handleUpdate = (updatedBuilding) => {
    setData((prevData) =>
      prevData.map((item) =>
        item.id === updatedBuilding.id ? { ...item, ...updatedBuilding } : item
      )
    );
    setShowViewBuildingForm(false);
  };
  const handleCloseViewModal = () => {
    setShowViewBuildingForm(false);
  }
  // console.log("Selected Building:" , selectedBuilding);
  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        title="Buildings"
        searchTerm={searchTerm}
        showSearch={true}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        onAdd={handleAdd}
        columns={columns}
        data={data}
        // showSearch={false}
        showAddButton={true}
      />
      {showAddBuildingForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-10 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
            <div className="rounded-lg  max-h-[90vh] overflow-y-auto relative">
              <AddBuildingForm
                onClose={handleCloseModal}
                onSubmit={(newBuilding) => {
                  setData([newBuilding, ...data]);
                  setShowAddBuildingForm(false);
                }}
              />
            </div>
          </div>
        </div>
      )}
      {showViewBuildingForm && selectedBuilding && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-10 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
            <div className="rounded-lg  max-h-[90vh] overflow-y-auto relative">
              <ViewEditBuildingForm
              onClose={handleCloseViewModal}
                buildingData={selectedBuilding}
                onUpdate={handleUpdate}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Building;
