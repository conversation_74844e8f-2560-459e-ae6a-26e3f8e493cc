import React, { useState, useEffect, useCallback } from "react";
import GenericTable from "../../GenericTable";
import AddDelegateForm from "./AddDelegateForm";
import ViewEditDelegateForm from "./ViewEditDelegateForm";
import Loader from "../../Loader.jsx";
import TruncatedRow from "../../Tooltip/TrucantedRow.jsx";
import { getDelegates, deleteDelegates } from "../../../api/identity";
import { toast } from "react-toastify";
import { useLocation } from "react-router-dom";
import deleted from "../../../Images/Delete.svg";

const Delegate = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddDelegateForm, setShowAddDelegateForm] = useState(false);
  const [showViewDelegateForm, setShowViewDelegateForm] = useState(false);
  const [selectedDelegate, setSelectedDelegate] = useState(null);

  // Get identity_id from URL parameters
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const identityId = queryParams.get("identity_id");

  // Fetch delegates data from API
  const fetchDelegatesData = useCallback(async () => {
    if (!identityId) return;

    setLoading(true);
    try {
      const params = {
        search: searchTerm || undefined,
      };
      const res = await getDelegates(identityId, params);
      // Ensure we always set an array
      setData(Array.isArray(res) ? res : []);
    } catch (error) {
      toast.error("Error fetching delegates data.");
      console.error("Error fetching delegates data:", error);
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [identityId, searchTerm]);

  useEffect(() => {
    fetchDelegatesData();
  }, [fetchDelegatesData]);

  const handleAdd = () => {
    setShowAddDelegateForm(true);
  };

  const handleCloseModal = () => {
    setShowAddDelegateForm(false);
  };

  const handleView = (row) => {
    setSelectedDelegate(row);
    setShowViewDelegateForm(true);
  };

  const handleDelete = async (delegate) => {
    try {
      await deleteDelegates(delegate.delegate_id || delegate.id);
      setData((prevData) =>
        prevData.filter((item) => (item.delegate_id || item.id) !== (delegate.delegate_id || delegate.id))
      );
      toast.success("Delegate deleted successfully!");
    } catch (error) {
      toast.error("Failed to delete delegate. Please try again.");
      console.error("Error deleting delegate:", error);
    }
  };

  const handleUpdateDelegate = (updatedDelegate) => {
    setData((prevData) =>
      prevData.map((item) =>
        (item.delegate_id || item.id) === (updatedDelegate.delegate_id || updatedDelegate.id) ? updatedDelegate : item
      )
    );
    setShowViewDelegateForm(false);
  };

  const handleAddDelegate = (newDelegate) => {
    setData((prev) => [
      { ...newDelegate, delegate_id: Date.now() },
      ...prev,
    ]);
    fetchDelegatesData(); // Refresh data from server
    setShowAddDelegateForm(false);
  };

  const columns = [
    {
      name: "Name",
      selector: (row) => row.name || row.delegate_name,
      cell: (row) => (
        <span
          style={{ textDecoration: "underline", cursor: "pointer" }}
          onClick={() => handleView(row)}
        >
         <TruncatedRow text={row.name || row.delegate_name || ""}/>
        </span>
      ),
      sortable: true,
    },
    {
      name: "UID",
      selector: (row) => row.uid || row.delegate_uid,
      cell:(row) => <TruncatedRow text={row.uid || row.delegate_uid || ""}/>
    },
    {
      name: "Task Types",
      selector: (row) => row.taskTypes || row.task_to_delegate,
      cell:(row) => <TruncatedRow text={row.taskTypes || row.task_to_delegate || ""}/>
    },
    {
      name: "Start Date",
      selector: (row) => row.start_date,
      cell:(row) => <TruncatedRow text={row.start_date ? new Date(row.start_date).toLocaleDateString() : ""}/>
    },
    {
      name: "End Date",
      selector: (row) => row.end_date,
      cell:(row) => <TruncatedRow text={row.end_date ? new Date(row.end_date).toLocaleDateString() : ""}/>
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center text-sm font-semibold rounded-full
            ${
            row.status.toLowerCase() === "active"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          } `}
        >
          {row.status}
        </span>
      ),
      center: true,
    },
    {
      name: "Action",
      cell: (row) => (
        <img
          src={deleted}
          alt="deleted"
          className="p-2 rounded-lg cursor-pointer bg-[#E21B1B14]"
          onClick={() => handleDelete(row)}
        />
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ];

  return (
    <div className="bg-white rounded-[10px] ">
      {loading ? (
        <Loader />
      ) : (
        <GenericTable
          title="Delegates"
          searchTerm={searchTerm}
          showSearch={true}
          onSearchChange={(e) => setSearchTerm(e.target.value)}
          onAdd={handleAdd}
          columns={columns}
          data={data}
          showAddButton={true}
        />
      )}
      {showAddDelegateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <AddDelegateForm
                onClose={handleCloseModal}
                onSubmit={handleAddDelegate}
              />
            </div>
          </div>
        </div>
      )}
      {showViewDelegateForm && selectedDelegate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewEditDelegateForm
                onClose={() => setShowViewDelegateForm(false)}
                delegateData={selectedDelegate}
                onUpdateDelegate={handleUpdateDelegate}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Delegate;