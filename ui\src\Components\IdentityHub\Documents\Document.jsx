import React, { useState, useEffect, useCallback } from "react";
import GenericTable from "../../GenericTable";
import AddDocumentForm from "./AddDocumentForm";
import ViewEditDocumentForm from "./ViewEditDocumentForm";
import TruncatedRow from "../../Tooltip/TrucantedRow.jsx";
import TruncatedCell from "../../Tooltip/TruncatedCell.jsx";
import { getDocument, deleteDocument } from "../../../api/identity";
import { toast } from "react-toastify";
import { useLocation } from "react-router-dom";
import deleted from "../../../Images/Delete.svg";

const Document = () => {
  const [documents, setDocuments] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState('document_name');
  const [sortOrder, setSortOrder] = useState('ASC');
  const [showAddDocumentForm, setShowAddDocumentForm] = useState(false);
  const [showViewDocumentForm, setShowViewDocumentForm] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);

  // Get identity_id from URL parameters
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const identityId = queryParams.get("identity_id");

  // Fetch documents data function
  const fetchDocuments = useCallback(async () => {
    if (!identityId) {
      console.warn("No identity ID provided");
      return;
    }

    try {
      console.log("Fetching documents for identity:", identityId);
      const response = await getDocument(identityId, {
        sortBy,
        sortOrder,
        search: searchQuery,
      });

      console.log("Documents API response:", response);

      // Handle different response structures
      let documentsData = [];
      if (Array.isArray(response)) {
        documentsData = response;
      } else if (response && Array.isArray(response.data)) {
        documentsData = response.data;
      } else if (response && response.documents && Array.isArray(response.documents)) {
        documentsData = response.documents;
      }

      // Map API response to consistent field names
      const mappedDocuments = documentsData.map(doc => ({
        id: doc.id || doc.document_id,
        document_name: doc.document_name || doc.name || doc.documentName,
        document_type: doc.document_type || doc.type || doc.documentType,
        document_number: doc.document_number || doc.number || doc.documentNumber,
        issue_date: doc.issue_date || doc.issueDate,
        expiry_date: doc.expiry_date || doc.expiryDate,
        status: doc.status || "Active"
      }));

      console.log("Mapped documents:", mappedDocuments);
      setDocuments(mappedDocuments);
    } catch (error) {
      console.error("Error fetching documents:", error);
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          "Error fetching documents data.";
      toast.error(errorMessage);
      setDocuments([]);
    }
  }, [identityId, sortBy, sortOrder, searchQuery]);

  // Fetch documents data on component mount and when dependencies change
  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleSortClick = (column, sortDirection) => {
    setSortBy(column.id);
    setSortOrder(sortDirection.toUpperCase());
  };

  const handleAdd = () => {
    setShowAddDocumentForm(true);
  };

  const handleView = (row) => {
    setSelectedDocument(row);
    setShowViewDocumentForm(true);
  };

  const handleDelete = async (document) => {
    if (!document.id) {
      toast.error("Invalid document ID");
      return;
    }

    // Add confirmation dialog
    if (!window.confirm(`Are you sure you want to delete "${document.document_name}"?`)) {
      return;
    }

    try {
      console.log("Deleting document with ID:", document.id);
      await deleteDocument(document.id);
      toast.success("Document deleted successfully!");
      fetchDocuments(); // Refresh the list
    } catch (error) {
      console.error("Error deleting document:", error);
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          "Failed to delete document. Please try again.";
      toast.error(errorMessage);
    }
  };

  const handleUpdate = async () => {
    setShowViewDocumentForm(false);
    fetchDocuments(); // Refresh the data
  };

  const handleAddDocument = async () => {
    setShowAddDocumentForm(false);
    fetchDocuments(); // Refresh the data
  };

  const columns = [
    {
      id: 'document_name',
      name: <TruncatedCell text="Document Name"/>,
      selector: (row) => row.document_name,
      cell: (row) => (
        <span
          className="underline underline-offset-1 cursor-pointer"
          onClick={() => handleView(row)}
        >
         <TruncatedRow text={row.document_name || ""}/>
        </span>
      ),
      sortable: true,
    },
    {
      id: 'document_type',
      name: <TruncatedCell text="Document Type" />,
      selector: (row) => row.document_type,
      cell:(row) => <TruncatedRow text={row.document_type || ""}/>,
      sortable: true,
    },
    {
      id: 'document_number',
      name: <TruncatedCell text="Document Number" />,
      selector: (row) => row.document_number,
      cell:(row) => <TruncatedRow text={row.document_number || ""}/>,
      sortable: true,
    },
    {
      id: 'issue_date',
      name: <TruncatedCell text="Issue Date" />,
      selector: (row) => row.issue_date,
      cell:(row) => <TruncatedRow text={row.issue_date ? new Date(row.issue_date).toLocaleDateString() : ""}/>,
      sortable: true,
    },
    {
      id: 'expiry_date',
      name: <TruncatedCell text="Expiry Date" />,
      selector: (row) => row.expiry_date,
      cell:(row) => <TruncatedRow text={row.expiry_date ? new Date(row.expiry_date).toLocaleDateString() : ""}/>,
      sortable: true,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center text-sm font-semibold rounded-full ${
            row.status === "Active" || row.status === "Valid"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
      center: true,
    },
    {
      name: "Action",
      cell: (row) => (
        <img
          src={deleted}
          alt="deleted"
          className="p-2 rounded-lg cursor-pointer bg-[#E21B1B14]"
          onClick={() => handleDelete(row)}
        />
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ];

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        title="Documents"
        searchTerm={searchQuery}
        onSearchChange={handleSearch}
        onSort={handleSortClick}
        columns={columns}
        onAdd={handleAdd}
        data={documents}
        showSearch={true}
        showAddButton={true}
      />

      {showAddDocumentForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <AddDocumentForm
                onClose={() => setShowAddDocumentForm(false)}
                onSubmit={handleAddDocument}
              />
            </div>
          </div>
        </div>
      )}

      {showViewDocumentForm && selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white p-1 shadow-lg rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewEditDocumentForm
                documentData={selectedDocument}
                onClose={() => setShowViewDocumentForm(false)}
                onUpdate={handleUpdate}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Document;