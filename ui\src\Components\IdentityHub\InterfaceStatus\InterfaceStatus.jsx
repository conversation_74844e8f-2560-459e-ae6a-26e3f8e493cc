import React, { useState} from "react";
import GenericTable from "../../GenericTable";
import AddInterfaceStatusForm from "./AddInterfaceStatusForm";
import ViewEditInterfaceStatusForm from "./ViewEditInterfaceStatusForm";
import deleted from "../../../Images/Delete.svg"
import TruncatedRow from "../../Tooltip/TrucantedRow";
import TruncatedCell from "../../Tooltip/TruncatedCell";

const InterfaceStatus = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [interfaceStatuses, setInterfaceStatuses] = useState([
    {
      id: 1,
      referenceId: "REF001",
      source: "System A",
      destination: "System B",
      sentDate: "2023-04-01",
      ackDate: "2023-04-02",
      status: "Active",
    },
    {
      id: 2,
      referenceId: "REF002",
      source: "System C",
      destination: "System D",
      sentDate: "2023-04-03",
      ackDate: "2023-04-04",
      status: "Inactive",
    },
    {
      id: 3,
      referenceId: "REF003",
      source: "System E",
      destination: "System F",
      sentDate: "2023-04-05",
      ackDate: "2023-04-06",
      status: "Active",
    },
  ]);

  const [showAddForm, setShowAddForm] = useState(false);
  const [showViewForm, setShowViewForm] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(null);

  const handleAdd = () => {
    setShowAddForm(true);
  };

  const handleDelete = (statusItem) => {
    setInterfaceStatuses((prev) =>
      prev.filter((item) => item.id !== statusItem.id)
    );
  };

  const handleView = (statusItem) => {
    setSelectedStatus(statusItem);
    setShowViewForm(true);
  };

  const handleUpdate = (updatedStatus) => {
    setInterfaceStatuses((prev) =>
      prev.map((item) => (item.id === updatedStatus.id ? updatedStatus : item))
    );
    setShowViewForm(false);
  };

  const columns = [
    {
      name:<TruncatedCell text="Reference ID"/>,
      selector: (row) => row.referenceId,
      cell: (row) => (
        <span
          className="underline underline-offset-1 cursor-pointer"
          onClick={() => handleView(row)}
        >
          <TruncatedRow text={row.referenceId}/>
        </span>
      ),
    },
    {
      name: <TruncatedCell text="Source"/>,
      selector: (row) => row.source,
      cell: (row) => <TruncatedRow text={row.source} />,
    },
    {
      name: <TruncatedCell text="Destination"/>,
      selector: (row) => row.destination,
      cell: (row) => <TruncatedRow text={row.destination} />,
    },
    {
      name: <TruncatedCell text="Sent Date"/>,
      selector: (row) => row.sentDate,
      cell: (row) => <TruncatedRow text={row.sentDate} />,
    },
    {
      name: <TruncatedCell text="Ack Date"/>,
      selector: (row) => row.ackDate,
      cell: (row) => <TruncatedRow text={row.ackDate} />,
    },
    {
      name: <TruncatedCell text="Status"/>,
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center text-sm rounded-full ${
            row.status === "Active"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: "Action",
      cell: (row) => (
        <img src={deleted} alt="deleted" 
        className="p-2 rounded-lg cursor-pointer bg-[#E21B1B14]"
        onClick={() => handleDelete(row)}/>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ];

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        title="Interface Status"
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        columns={columns}
        data={interfaceStatuses}
        showSearch={true}
        showAddButton={true}
        onAdd={handleAdd}
      />

      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <AddInterfaceStatusForm
                onClose={() => setShowAddForm(false)}
                onSubmit={(newStatus) => {
                  setInterfaceStatuses((prev) => [newStatus, ...prev]);
                  setShowAddForm(false);
                }}
              />
            </div>
          </div>
        </div>
      )}

      {showViewForm && selectedStatus && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white p-1 shadow-lg rounded-lg ">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewEditInterfaceStatusForm
                statusData={selectedStatus}
                onClose={() => setShowViewForm(false)}
                onUpdate={handleUpdate}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InterfaceStatus;
