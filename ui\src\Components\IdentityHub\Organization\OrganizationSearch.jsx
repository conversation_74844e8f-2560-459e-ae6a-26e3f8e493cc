import React, { useState, useRef, useEffect } from "react";
import SearchBar from "../../Components/Global/SearchBar";

const OrganizationSearch = () => {
  // Static organization data
  const staticOrganizations = [
    { id: 1, name: "Caremate", image: "https://via.placeholder.com/40", site: "Bangalore" },
    { id: 2, name: "Corp HQ", image: "https://via.placeholder.com/40", site: "Mumbai" },
    { id: 3, name: "Tech Innovators", image: "https://via.placeholder.com/40", site: "Delhi" },
    { id: 4, name: "Alpha Solutions", image: "https://via.placeholder.com/40", site: "Chennai" },
  ];

  const [searchTerm, setSearchTerm] = useState("");
  const [results, setResults] = useState(staticOrganizations);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const containerRef = useRef(null);

  const onInputChange = (e) => {
    const term = e.target.value;
    setSearchTerm(term);
    if (term.trim() !== "") {
      const filtered = staticOrganizations.filter((org) =>
        org.name.toLowerCase().includes(term.toLowerCase())
      );
      setResults(filtered);
      setIsDropdownVisible(true);
    } else {
      setResults(staticOrganizations);
      setIsDropdownVisible(false);
    }
  };

  const onSearchSubmit = (e) => {
    e.preventDefault();
  };

  const onResultClick = (org) => {
    setSearchTerm(org.name);
    setIsDropdownVisible(false);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsDropdownVisible(false);
      }
    };
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  return (
    <div className="w-full sm:w-auto relative" ref={containerRef}>
      <form onSubmit={onSearchSubmit}>
        <SearchBar
          placeholder="Search Organization"
          iconSrc=""
          onInputChange={onInputChange}
          value={searchTerm}
          borderColor="#4F2683"
        />
      </form>
      {isDropdownVisible && (
        <div
          className="w-full mt-2 border absolute p-2 bg-white z-10 rounded-md shadow-lg overflow-y-auto"
          style={{ maxHeight: "200px" }}
        >
          {results.length > 0 ? (
            results.map((org) => (
              <div
                key={org.id}
                className="flex items-center gap-2 p-2 border-b cursor-pointer hover:bg-gray-100"
                onClick={() => onResultClick(org)}
              >
                <img src={org.image} alt={org.name} className="w-10 h-10 rounded-full" />
                <div>
                  <h2 className="font-semibold">{org.name}</h2>
                  <div className="flex flex-row gap-4">
                    <p className="text-sm text-gray-600">ID: {org.id}</p>
                    <p className="text-sm text-gray-600">{org.site}</p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <p className="text-gray-500">No results found.</p>
          )}
        </div>
      )}
    </div>
  );
};

export default OrganizationSearch;
