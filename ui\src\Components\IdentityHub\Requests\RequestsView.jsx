import React, { useState } from "react";
import GenericTable from "../../GenericTable";

const RequestsView = ({ onClose, requestData }) => {
  const [activeTab, setActiveTab] = useState("Area");
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const areaColumns = [
    { name: "Area", selector: (row) => row.area },
    { name: "Code", selector: (row) => row.code },
    { name: "Location", selector: (row) => row.location },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`px-2 py-1 rounded-full text-sm font-semibold ${row.status === "Active"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
            }`}
        >
          {row.status}
        </span>
      ),
    },
  ];
  const areaData = [
    { area: "Zone 1", code: "Z1", location: "Building A", status: "Active" },
    { area: "Zone 2", code: "Z2", location: "Building B", status: "Inactive" },
    // ...add more rows as needed
  ];

  const approvalsColumns = [
    { name: "Approver", selector: (row) => row.approver },
    { name: "Role", selector: (row) => row.role },
    { name: "Status", selector: (row) => row.status },
    { name: "Approved On", selector: (row) => row.approvedOn },
  ];
  const approvalsData = [
    {
      approver: "Alice",
      role: "Manager",
      status: "Approved",
      approvedOn: "2024-01-31",
    },
    {
      approver: "Bob",
      role: "Director",
      status: "Pending",
      approvedOn: "2024-02-01",
    },
    // ...add more rows as needed
  ];

  // Render the appropriate table based on the active tab
  const renderTable = () => {
    switch (activeTab) {
      // case "Identity":
      //   return (
      //     <GenericTable
      //       columns={identityColumns}
      //       data={identityData}
      //       showSearch={false}
      //       showAddButton={false}
      //     />
      //   );
      case "Area":
        return (
          <GenericTable
            columns={areaColumns}
            data={areaData}
            showSearch={false}
            showAddButton={false}
          />
        );
      case "Approvals":
        return (
          <GenericTable
            columns={approvalsColumns}
            data={approvalsData}
            showSearch={false}
            showAddButton={false}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px]  shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"
          }`}
        style={{ willChange: "transform" }}
      >  {/* Header */}
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-[24px] md:text-[30px] font-normal text-[#4F2683]">
            View Request Details
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        {/* <hr className="mx-3 mb-4" /> */}
        <div className="p-6">
        <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">
          {/* Request Details - Single Column Layout */}
          <div className="p-6 mb-6 text-[14px] md:text-[16px] text-[#333333] space-y-2">
            <div>
              <strong>Request ID:</strong> {requestData.requestId}
            </div>
            <div>
              <strong>Requested Date:</strong> {requestData.requestedDate}
            </div>
            <div>
              <strong>Requested For:</strong> {requestData.requestedFor}
            </div>
            <div>
              <strong>Approver:</strong> {requestData.approver}
            </div>
            <div>
              <strong>Start Date:</strong> {requestData.startDate}
            </div>
            <div>
              <strong>End Date:</strong> {requestData.endDate}
            </div>
            <div>
              <strong>Requested By:</strong> {requestData.requestedBy}
            </div>
            <div>
              <strong>Justification:</strong> {requestData.justification}
            </div>
         

          {/* Tabs */}
          <div>
            <div className="flex  my-4 gap-2">
              {/* <button
              onClick={() => setActiveTab("Identity")}
              className={`px-4 py-2 rounded-full ${
                activeTab === "Identity"
                  ? "bg-[#4F2683] text-white"
                  : "bg-gray-200 text-[#333]"
              }`}
            >
              Identity
            </button> */}
              <button
                onClick={() => setActiveTab("Area")}
                className={`px-4 py-2 rounded-full ${activeTab === "Area"
                    ? "bg-[#4F2683] text-white"
                    : "bg-gray-200 text-[#333]"
                  }`}
              >
                Area
              </button>
              <button
                onClick={() => setActiveTab("Approvals")}
                className={`px-4 py-2 rounded-full ${activeTab === "Approvals"
                    ? "bg-[#4F2683] text-white"
                    : "bg-gray-200 text-[#333]"
                  }`}
              >
                Approvals
              </button>
            </div>
          </div>

          {/* Table */}
          <div>{renderTable()}</div>
        </div>
         </div>
        </div>
      </div>
    </div>
  );
};

export default RequestsView;
