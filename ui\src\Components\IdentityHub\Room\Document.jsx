import React, { useState } from "react";
import GenericTable from "../../GenericTable";
import AddDocumentForm from "./AddRoomForm";
import ViewEditDocumentForm from "./ViewEditRoomForm";
import Swal from "sweetalert2";
import withReactContent from "sweetalert2-react-content";
import PrintIcon from "../../../Images/print.svg"
import view from "../../../Images/view.png"
import deleted from "../../../Images/Delete.svg"
import TruncatedCell from "../../Tooltip/TruncatedCell";
import TruncatedRow from "../../Tooltip/TrucantedRow";
const MySwal = withReactContent(Swal);

const Documents = () => {
  const [documents, setDocuments] = useState([
    {
      id: 1,
      documentName: "Passport",
      documentNumber: "A1234567",
      issueDate: "2023-01-01",
      expirationDate: "2033-01-01",
      country: "USA",
      state: "NY",
      uploadedDate: "2023-04-01",
      attachment: "https://example.com/passport.pdf",
      status: "Active",
      otherIssuer: "",
      note: "",
    },
  ]);

  const [showAddDocumentForm, setShowAddDocumentForm] = useState(false);
  const [showViewEditDocumentForm, setShowViewEditDocumentForm] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);

  const handleEditDocument = (doc) => {
    setSelectedDocument(doc);
    setShowViewEditDocumentForm(true);
  };

  const handleViewDocument = (doc) => {
    MySwal.fire({
      title: `<div class='text-[#4F2683] text-2xl font-normal'>Document Preview</div>`,
      showCloseButton: true,
      width: "60%",
      heightAuto: false,
      scrollable: true,
      html: `
        <div style="max-height: 500px; overflow-y: auto; padding: 10px;">
          <p><strong>Name:</strong> ${doc.documentName}</p>
          <p><strong>Document Number:</strong> ${doc.documentNumber}</p>
          <p><strong>Uploaded:</strong> ${doc.uploadedDate}</p>
          <object data="${doc.attachment}" type="application/pdf" class="w-full h-[500px] border mt-4">
            <div style="text-align: center; padding: 20px;">
              <p>Your browser does not support inline viewing of this file.</p>
              <a 
                href="${doc.attachment}" 
                download="${doc.documentName}" 
                style="display:inline-block; margin-top:10px; padding:10px 20px; background-color:#4F2683; color:#fff; border-radius:5px; text-decoration:none;"
              >
                Download File
              </a>
            </div>
          </object>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: "OK",
    });
  };

  const handleDeleteDocument = (id) => {
    setDocuments((prevDocs) => prevDocs.filter((doc) => doc.id !== id));
  };

  const handlePrint = (doc) => {
    if (doc.attachment) {
      const printWindow = window.open(doc.attachment, "_blank");
      printWindow && printWindow.print();
    }
  };

  const handleUpdate = (updatedDoc) => {
    setDocuments((prevDocs) =>
      prevDocs.map((doc) => (doc.id === updatedDoc.id ? updatedDoc : doc))
    );
    setShowViewEditDocumentForm(false);
  };

  const columns = [
    {
      name:<TruncatedCell text= "Document Name"/>,
      selector: (row) => row.documentName,
      cell: (row) => (
        
        <span className="underline cursor-pointer" onClick={() => handleEditDocument(row)}>
          <TruncatedRow text={row.documentName}/>
        </span>
      ),
      sortable: true,
    },
    { name:<TruncatedCell text= "Document Number"/>, 
      selector: (row) => row.documentNumber ,
      cell: (row) => <TruncatedRow text={row.documentNumber}/>},

    { name: <TruncatedCell text="Issue Date"/>, 
      selector: (row) => row.issueDate ,
      cell: (row) => <TruncatedRow text={row.issueDate}/>},

    { name:<TruncatedCell text= "Expiration Date"/>, 
      selector: (row) => row.expirationDate ,
      cell: (row) => <TruncatedRow text={row.expirationDate}/>},

    { name:<TruncatedCell text="Country"/>, 
      selector: (row) => row.country ,
      cell: (row) => <TruncatedRow text={row.country}/>},

    { name: <TruncatedCell text="State"/>, 
      selector: (row) => row.state ,
      cell: (row) => <TruncatedRow text={row.state}/>},

    { name:<TruncatedCell text="Uploaded Date"/>, 
      selector: (row) => row.uploadedDate ,
      cell: (row) => <TruncatedRow text={row.uploadedDate}/>},
    {
      name:<TruncatedCell text="View Document"/>,
      cell: (row) => (
      <img   src={view}
      alt="view"
      className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
      onClick={() => handleViewDocument(row)} />
      ),
      center: true,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center  rounded-full ${
            row.status.toLowerCase() === "active"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: "Action",
      cell: (row) => (
        <div className="flex items-center justify-center space-x-2">
        
       <img   src={PrintIcon}
            alt="Print"
            className="p-1.5 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => handlePrint(row)} />
            <img src={deleted} alt="deleted"
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer"
            onClick={() => handleDeleteDocument(row.id)} />
        </div>
      ),
    },
  ];

  return (
    <div className="w-full bg-white shadow rounded-[10px] border">
      <GenericTable
        title="Documents"
        searchTerm={""}
        onSearchChange={() => {}}
        onAdd={() => setShowAddDocumentForm(true)}
        columns={columns}
        data={documents}
        showSearch={true}
        fixedHeaderScrollHeight="400px"
        showAddButton={true}
      />
      {showAddDocumentForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-10 flex justify-center items-center">
          <div className="bg-white shadow-lg p-4 rounded-lg w-[80%] max-h-[90vh] overflow-y-auto">
            <AddDocumentForm
              onClose={() => setShowAddDocumentForm(false)}
              onSubmit={(newDoc) => {
                newDoc.id = Date.now();
                setDocuments((prev) => [newDoc, ...prev]);
                setShowAddDocumentForm(false);
              }}
            />
          </div>
        </div>
      )}
      {showViewEditDocumentForm && selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-10 flex justify-center items-center">
          <div className="bg-white shadow-lg p-4 rounded-lg w-[80%] max-h-[90vh] overflow-y-auto">
            <ViewEditDocumentForm
              documentData={selectedDocument}
              onClose={() => setShowViewEditDocumentForm(false)}
              onUpdate={handleUpdate}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Documents;
