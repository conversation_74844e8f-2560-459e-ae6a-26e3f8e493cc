import React, { useState } from "react";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";

const ViewEditTasksForm = ({ taskData, onUpdate, onClose }) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState({
    id: taskData.id || "",
    taskId: taskData.taskId || "",
    type: taskData.type || "",
    requestId: taskData.requestId || "",
    requestedBy: taskData.requestedBy || "",
    createdOn: taskData.createdOn || "",
    justification: taskData.justification || "",
    requestedFor: taskData.requestedFor || "",
    items: taskData.items || "",
    status: taskData.status || "",
  });
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = (e) => {
    e.preventDefault();
    onUpdate(formData);
    setIsEditMode(false);
  };

  const inputClassName = `w-full border bg-transparent rounded p-2 ${
    isEditMode ? "focus:outline-none" : "border-none text-[#8F8F8F]"
  }`;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Document</h2>
           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        <form onSubmit={handleSave} className="bg-white p-6 rounded-lg">
          {/* Task ID */}
          <div className="flex items-center mb-4">
            <label htmlFor="taskId" className="w-1/4 text-[16px] font-normal">
              Task ID
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="taskId"
                id="taskId"
                value={formData.taskId}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          {/* Type */}
          <div className="flex items-center mb-4">
            <label htmlFor="type" className="w-1/4 text-[16px] font-normal">
              Type
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="type"
                id="type"
                value={formData.type}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          {/* Request ID */}
          <div className="flex items-center mb-4">
            <label htmlFor="requestId" className="w-1/4 text-[16px] font-normal">
              Request ID
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="requestId"
                id="requestId"
                value={formData.requestId}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          {/* Requested By */}
          <div className="flex items-center mb-4">
            <label htmlFor="requestedBy" className="w-1/4 text-[16px] font-normal">
              Requested By
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="requestedBy"
                id="requestedBy"
                value={formData.requestedBy}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          {/* Created On */}
          <div className="flex items-center mb-4">
            <label htmlFor="createdOn" className="w-1/4 text-[16px] font-normal">
              Created On
            </label>
            <div className="w-3/4">
              <Input
                type="date"
                name="createdOn"
                id="createdOn"
                value={formData.createdOn}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          {/* Justification */}
          <div className="flex items-center mb-4">
            <label htmlFor="justification" className="w-1/4 text-[16px] font-normal">
              Justification
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="justification"
                id="justification"
                value={formData.justification}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          {/* Requested For */}
          <div className="flex items-center mb-4">
            <label htmlFor="requestedFor" className="w-1/4 text-[16px] font-normal">
              Requested For
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="requestedFor"
                id="requestedFor"
                value={formData.requestedFor}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          {/* Item(s) */}
          <div className="flex items-center mb-4">
            <label htmlFor="items" className="w-1/4 text-[16px] font-normal">
              Item(s)
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="items"
                id="items"
                value={formData.items}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          {/* Status */}
          <div className="flex items-center mb-4">
            <label htmlFor="status" className="w-1/4 text-[16px] font-normal">
              Status
            </label>
            <div className="w-3/4">
              {isEditMode ? (
                <CustomDropdown
                  className="h-11 rounded border-gray-300"
                  options={["Pending", "In Progress", "Completed"]}
                  onSelect={(option) =>
                    setFormData({ ...formData, status: option })
                  }
                  selectedOption={formData.status}
                  value={formData.status}
                  hoverBgColor="hover:bg-[#4F2683]"
                />
              ) : (
                <Input
                  type="text"
                  name="status"
                  id="status"
                  value={formData.status}
                  disabled
                  className={inputClassName}
                />
              )}
            </div>
          </div>

          <div className="flex gap-4 justify-end">
            {!isEditMode ? (
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  setIsEditMode(true);
                }}
                className="px-4 py-2 bg-[#4F2683] text-white rounded"
              >
                Edit
              </button>
            ) : (
              <>
                <button
                  type="button"
                  onClick={() => {
                    setIsEditMode(false);
                    setFormData({
                      id: taskData.id || "",
                      taskId: taskData.taskId || "",
                      type: taskData.type || "",
                      requestId: taskData.requestId || "",
                      requestedBy: taskData.requestedBy || "",
                      createdOn: taskData.createdOn || "",
                      justification: taskData.justification || "",
                      requestedFor: taskData.requestedFor || "",
                      items: taskData.items || "",
                      status: taskData.status || "",
                    });
                  }}
                  className="px-4 py-2 bg-gray-400 text-white rounded"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[#4F2683] text-white rounded"
                >
                  Save
                </button>
              </>
            )}
          </div>
        </form>
        </div>
        </div>
      </div>
    </div>
  );
};

export default ViewEditTasksForm;
