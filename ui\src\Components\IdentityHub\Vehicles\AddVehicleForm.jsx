import React, { useState } from "react";
import Button from "../../Global/Button";
import Input from "../../Global/Input/Input";
// import CustomDropdown from "../../Global/CustomDropdown";
import DateInput from "../../Global/Input/DateInput";
import { createVehicle } from "../../../api/identity";
import { toast } from "react-toastify";
import { useLocation } from "react-router-dom";

const AddVehicleForm = ({ onSubmit, onClose }) => {
  const [plateNumber, setPlateNumber] = useState("");
  const [issuedBy, setIssuedBy] = useState("");
  const [VIN, setVIN] = useState("");
  const [year, setYear] = useState("");
  const [make, setMake] = useState("");
  const [model, setModel] = useState("");
  const [color, setColor] = useState("");
  const [uploadedDate, setUploadedDate] = useState("");
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(false);

  // Get identity_id from URL parameters
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const identityId = queryParams.get("identity_id");

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields and show specific field names in toast
    const requiredFields = [];

    if (!plateNumber) {
      requiredFields.push("Plate Number");
    }
    if (!issuedBy) {
      requiredFields.push("Issued by");
    }
    if (!VIN) {
      requiredFields.push("VIN");
    }
    if (!year) {
      requiredFields.push("Year");
    }
    if (!make) {
      requiredFields.push("Make");
    }
    if (!model) {
      requiredFields.push("Model");
    }
    if (!uploadedDate) {
      requiredFields.push("Uploaded Date");
    }

    if (requiredFields.length > 0) {
      const fieldNames = requiredFields.join(", ");
      toast.error(`Please fill the following required field(s): ${fieldNames}`);
      return;
    }

    setLoading(true);
    try {
      const vehicleData = {
        plate_number: plateNumber,
        issued_by: issuedBy,
        vin: VIN,
        year: year,
        make: make,
        model: model,
        color: color,
        uploaded_date: uploadedDate,
        identity_id: identityId,
      };

      const result = await createVehicle(vehicleData);
      toast.success("Vehicle added successfully!");
      onSubmit(result);
      onClose();
    } catch (error) {
      console.error("Error adding vehicle:", error);
      toast.error("Failed to add vehicle. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Vehicle</h2>
           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        <form onSubmit={handleSubmit} className="p-6">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">
            Vehicle Details
          </h2>

          {/* Plate Number */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="plateNumber"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Plate Number*
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                id="plateNumber"
                placeholder="Plate Number"
                value={plateNumber}
                onChange={(e) => setPlateNumber(e.target.value)}
                required
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
          </div>

          {/* Issued by */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="issuedBy"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Issued by*
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                id="issuedBy"
                placeholder="Issued by"
                value={issuedBy}
                onChange={(e) => setIssuedBy(e.target.value)}
                required
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
          </div>

          {/* VIN */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="VIN"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              VIN*
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                id="VIN"
                placeholder="VIN"
                value={VIN}
                onChange={(e) => setVIN(e.target.value)}
                required
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
          </div>

          {/* Year */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="year"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Year*
            </label>
            <div className="w-3/4">
              <Input
                type="number"
                id="year"
                placeholder="Year"
                value={year}
                onChange={(e) => setYear(e.target.value)}
                required
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
          </div>

          {/* Make */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="make"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Make*
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                id="make"
                placeholder="Make"
                value={make}
                onChange={(e) => setMake(e.target.value)}
                required
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
          </div>

          {/* Model */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="model"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Model*
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                id="model"
                placeholder="Model"
                value={model}
                onChange={(e) => setModel(e.target.value)}
                required
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
          </div>

          {/* Color */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="color"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Color
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                id="color"
                placeholder="Color"
                value={color}
                onChange={(e) => setColor(e.target.value)}
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
          </div>

          {/* Uploaded Date */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="uploadedDate"
              className="w-1/4 text-[16px] font-normal text-[#333333]"
            >
              Uploaded Date*
            </label>
            <div className="w-3/4">
              <DateInput
                          name="uploadedDate"
                          className=" w-full"
                          id="uploadedDate"
                          value={uploadedDate}
                          onChange={(date) => setUploadedDate(date ? date.toISOString().split("T")[0] : "")}
                          placeholder="MM-DD-YYYY"
                        />
            </div>
          </div>

          <div className="flex gap-4 pb-4 justify-center">
            <Button
              type="cancel"
              label="Cancel"
              onClick={onClose}
              className="bg-gray-400 text-white"
            />
            <Button
              type="primary"
              label={loading ? "Adding..." : "Add"}
              disabled={loading}
              className="text-white"
            />
          </div>
        </form>
        </div>
        </div>
      </div>
    </div>
  );
};

export default AddVehicleForm;
