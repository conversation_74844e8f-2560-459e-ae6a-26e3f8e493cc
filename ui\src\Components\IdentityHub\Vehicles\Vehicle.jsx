import React, { useState, useEffect, useMemo, useRef, useCallback } from "react";
import GenericTable from "../../GenericTable";
import AddVehicleForm from "./AddVehicleForm";
import ViewEditVehicleForm from "./ViewEditVehicleForm";
import deleted from "../../../Images/Delete.svg";
import TruncatedRow from "../../Tooltip/TrucantedRow";
import TruncatedCell from "../../Tooltip/TruncatedCell";
import { getVehicle, deleteVehicle } from "../../../api/identity";
import { toast } from "react-toastify";
import { useLocation } from "react-router-dom";
import Loader from "../../Loader.jsx";

const Vehicle = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortConfig, setSortConfig] = useState({
    sortBy: 'plate_number',
    sortOrder: 'ASC'
  });
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showAddVehicleForm, setShowAddVehicleForm] = useState(false);
  const [showViewVehicleForm, setShowViewVehicleForm] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState(null);

  // Request control refs
  const abortControllerRef = useRef(new AbortController());
  const timeoutRef = useRef(null);
  const lastRequestHashRef = useRef(null);
  const isMountedRef = useRef(true);

  // Get identity_id from URL
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const identityId = queryParams.get("identity_id");

  // Generate hash for request params
  const generateRequestHash = useCallback((params) => {
    return JSON.stringify({
      identityId,
      search: params.search,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder
    });
  }, [identityId]);

  // Memoized API params
  const apiParams = useMemo(() => ({
    search: searchQuery || undefined,
    sortBy: sortConfig.sortBy,
    sortOrder: sortConfig.sortOrder,
  }), [searchQuery, sortConfig.sortBy, sortConfig.sortOrder]);

  const fetchVehicles = useCallback(async (params) => {
    if (!identityId || !isMountedRef.current) return;

    const currentHash = generateRequestHash(params);
    
    // Skip if identical to last request
    if (lastRequestHashRef.current === currentHash) {
      return;
    }

    // Cancel previous request
    abortControllerRef.current.abort();
    abortControllerRef.current = new AbortController();
    lastRequestHashRef.current = currentHash;

    try {
      setLoading(true);
      console.log("API call with params:", params);
      const res = await getVehicle(identityId, {
        ...params,
        signal: abortControllerRef.current.signal
      });
      
      if (isMountedRef.current) {
        setVehicles(Array.isArray(res) ? res : []);
      }
    } catch (error) {
      if (error.name !== 'AbortError' && isMountedRef.current) {
        toast.error("Error fetching vehicles data.");
        console.error("Error fetching vehicles data:", error);
        setVehicles([]);
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [identityId, generateRequestHash]);

  useEffect(() => {
    isMountedRef.current = true;
    
    // Clear any pending timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set new debounced timeout
    timeoutRef.current = setTimeout(() => {
      fetchVehicles(apiParams);
    }, 300);

    return () => {
      isMountedRef.current = false;
      clearTimeout(timeoutRef.current);
      abortControllerRef.current.abort();
    };
  }, [apiParams, fetchVehicles]);

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleSortClick = (column, sortDirection) => {
    setSortConfig({
      sortBy: column.id,
      sortOrder: sortDirection.toUpperCase()
    });
  };

  const handleAdd = () => {
    setShowAddVehicleForm(true);
  };

  const handleDelete = async (vehicle) => {
    try {
      await deleteVehicle(vehicle.vehicle_id || vehicle.id);
      setVehicles(prev => prev.filter(v => (v.vehicle_id || v.id) !== (vehicle.vehicle_id || vehicle.id)));
      lastRequestHashRef.current = null; // Force refresh on next load
      toast.success("Vehicle deleted successfully!");
    } catch (error) {
      toast.error("Failed to delete vehicle. Please try again.");
      console.error("Error deleting vehicle:", error);
    }
  };

  const handleView = (vehicle) => {
    setSelectedVehicle(vehicle);
    setShowViewVehicleForm(true);
  };

  const handleUpdate = (updatedVehicle) => {
    setVehicles(prev => prev.map(v => 
      (v.vehicle_id || v.id) === (updatedVehicle.vehicle_id || updatedVehicle.id) ? updatedVehicle : v
    ));
    setShowViewVehicleForm(false);
  };

  const handleAddVehicle = () => {
    setShowAddVehicleForm(false);
    lastRequestHashRef.current = null; // Force refresh on next load
    fetchVehicles(apiParams);
  };

  const columns = [
    {
      id: 'plate_number',
      name: <TruncatedCell text="Plate Number"/>,
      selector: (row) => row.plateNumber || row.plate_number,
      cell: (row) => (
        <span
          className="underline underline-offset-1 cursor-pointer"
          onClick={() => handleView(row)}
        >
          <TruncatedRow text={row.plateNumber || row.plate_number || ""}/>
        </span>
      ),
      sortable: true,
    },
    {
      id: 'issued_by',
      name: <TruncatedCell text="Issued by"/>,
      selector: (row) => row.issuedBy || row.issued_by,
      cell: (row) => <TruncatedRow text={row.issuedBy || row.issued_by || ""} />,
      sortable: true,
    },
    {
      id: 'vin',
      name: <TruncatedCell text="VIN"/>,
      selector: (row) => row.VIN || row.vin,
      cell: (row) => <TruncatedRow text={row.VIN || row.vin || ""} />,
      sortable: true,
    },
    {
      id: 'year',
      name: <TruncatedCell text="Year"/>,
      selector: (row) => row.year,
      cell: (row) => <TruncatedRow text={row.year || ""} />,
      sortable: true,
    },
    {
      id: 'make',
      name: <TruncatedCell text="Make"/>,
      selector: (row) => row.make,
      cell: (row) => <TruncatedRow text={row.make || ""} />,
      sortable: true,
    },
    {
      id: 'model',
      name: <TruncatedCell text="Model"/>,
      selector: (row) => row.model,
      cell: (row) => <TruncatedRow text={row.model || ""} />,
      sortable: true,
    },
    {
      id: 'color',
      name: <TruncatedCell text="Color"/>,
      selector: (row) => row.color,
      cell: (row) => <TruncatedRow text={row.color || ""} />,
      sortable: true,
    },
    {
      id: 'uploaded_date',
      name: <TruncatedCell text="Uploaded Date"/>,
      selector: (row) => row.uploadedDate || row.uploaded_date || row.created_at,
      cell: (row) => <TruncatedRow text={
        row.uploadedDate ||
        (row.uploaded_date ? new Date(row.uploaded_date).toLocaleDateString() : "") ||
        (row.created_at ? new Date(row.created_at).toLocaleDateString() : "")
      } />,
      sortable: true,
    },
    {
      name: "Action",
      cell: (row) => (
        <img 
          src={deleted}
          alt="Delete"
          className="p-2 rounded-lg cursor-pointer bg-[#E21B1B14]"
          onClick={() => handleDelete(row)} 
        />
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },  
  ];

  return (
    <div className="bg-white rounded-[10px]">
      {loading ? (
        <Loader />
      ) : (
        <GenericTable
          title="Vehicles"
          searchTerm={searchQuery}
          onSearchChange={handleSearch}
          onSort={handleSortClick}
          columns={columns}
          onAdd={handleAdd}
          data={vehicles}
          showSearch={true}
          showAddButton={true}
        />
      )}

      {showAddVehicleForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <AddVehicleForm
                onClose={() => setShowAddVehicleForm(false)}
                onSubmit={handleAddVehicle}
              />
            </div>
          </div>
        </div>
      )}

      {showViewVehicleForm && selectedVehicle && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white p-1 shadow-lg rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewEditVehicleForm
                vehicleData={selectedVehicle}
                onClose={() => setShowViewVehicleForm(false)}
                onUpdate={handleUpdate}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Vehicle;