import React, { useState } from "react";
import GenericTable from "../../GenericTable"; // Import GenericTable
import deletedIcon from "../../../Images/Delete.svg"; // Import delete icon
import TruncatedCell from "../../Tooltip/TruncatedCell"; // Import TruncatedCell for tooltips
import TruncatedRow from "../../Tooltip/TrucantedRow"; // Import TruncatedRow for tooltips

const ViewAreaAssignments = ({ onClose }) => {
  const [assignmentsData, setAssignmentsData] = useState([
    {
      identity: "AKSHAY SARDANA",
      corpId: "300016",
      startDate: "Jan-25-2024",
      endDate: "Jan-25-2024",
      timeCode: "-",
      cardType: "Multitech General",
      cardNo: "242421",
      cardStatus: "Valid",
      status: "Assigned",
    },
    // ...add more rows as needed...
  ]);
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleDelete = (corpId) => {
    // Function to delete a row based on Corp ID
    setAssignmentsData(assignmentsData.filter((row) => row.corpId !== corpId));
  };

  const columns = [
    {
      name: <TruncatedCell text="Identity" />,
      selector: (row) => row.identity,
      cell: (row) => <TruncatedRow text={row.identity} />,
      sortable: true,
    },
    {
      name: <TruncatedCell text="Corp ID" />,
      selector: (row) => row.corpId,
      cell: (row) => <TruncatedRow text={row.corpId} />,
    },
    {
      name: <TruncatedCell text="Start Date" />,
      selector: (row) => row.startDate,
      cell: (row) => <TruncatedRow text={row.startDate} />,
    },
    {
      name: <TruncatedCell text="End Date" />,
      selector: (row) => row.endDate,
      cell: (row) => <TruncatedRow text={row.endDate} />,
    },
    {
      name: <TruncatedCell text="Time Code" />,
      selector: (row) => row.timeCode,
      cell: (row) => <TruncatedRow text={row.timeCode} />,
    },
    {
      name: <TruncatedCell text="Card Type" />,
      selector: (row) => row.cardType,
      cell: (row) => <TruncatedRow text={row.cardType} />,
    },
    {
      name: <TruncatedCell text="Card No." />,
      selector: (row) => row.cardNo,
      cell: (row) => <TruncatedRow text={row.cardNo} />,
    },
    {
      name: <TruncatedCell text="Card Status" />,
      selector: (row) => row.cardStatus,
      cell: (row) => <TruncatedRow text={row.cardStatus} />,
    },
    {
      name: <TruncatedCell text="Status" />,
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center rounded-full ${
            row.status.toLowerCase() === "assigned"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: <TruncatedCell text="Action" />,
      cell: (row) => (
        <div className="flex gap-2">
          <img
            src={deletedIcon}
            onClick={() => handleDelete(row.corpId)}
            alt="deletedicon"
            className={`bg-[#E21B1B14] px-1 py-1 rounded ${
              row.status.toLowerCase() === "returned"
                ? "opacity-50 cursor-not-allowed"
                : "cursor-pointer"
            }`}
          />
        </div>
      ),
    },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`w-full max-w-7xl bg-white rounded-lg shadow-lg h-full overflow-y-auto transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        {/* Header Section */}
        <div className="flex items-center justify-between px-6 py-4 border-b">
          <h2 className="text-2xl font-semibold text-[#4F2683]">View Area Assignments</h2>
          <button
            className="w-8 h-8 text-xl bg-[#4F2683] text-white rounded-full flex items-center justify-center"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>

        {/* Details Section */}
        <div className="px-6 py-4">
          <div className="grid gap-4">
            <div className="flex items-center">
              <p className="text-sm font-medium text-gray-600 w-1/3">Access Area</p>
              <p className="text-base text-gray-800">ZTEST-LENEL-APAC-1</p>
            </div>
            <div className="flex items-center">
              <p className="text-sm font-medium text-gray-600 w-1/3">Assignments</p>
              <p className="text-base text-gray-800">10</p>
            </div>
            <div className="flex items-center">
              <p className="text-sm font-medium text-gray-600 w-1/3">Owner</p>
              <p className="text-base text-gray-800">DAVID STOLLER, 129802, AKSHAY SARDA...</p>
            </div>
            <div className="flex items-center">
              <p className="text-sm font-medium text-gray-600 w-1/3">Status</p>
              <p className="text-base text-gray-800">Active</p>
            </div>
          </div>
        </div>

        {/* Table Section */}
        <div className="p-6">
          <GenericTable
            showAddButton={false}
            columns={columns}
            data={assignmentsData}
            fixedHeader
            fixedHeaderScrollHeight="400px"
            highlightOnHover
            striped
          />
        </div>
      </div>
    </div>
  );
};

export default ViewAreaAssignments;