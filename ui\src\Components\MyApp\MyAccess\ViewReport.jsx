import React, { useState } from "react";
import GenericTable from "../../GenericTable";
import CustomDropdown from "../../Global/CustomDropdown"; // Import CustomDropdown

const ViewReport = ({ onClose }) => {
  const [owner, setOwner] = useState(null); // State for Owner dropdown
  const [approverLevel, setApproverLevel] = useState(null); // State for Approver Level dropdown
  const [department, setDepartment] = useState(null); // State for Department dropdown

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-5xl bg-white rounded-lg shadow-lg">
        {/* Header Section */}
        <div className="flex items-center justify-between px-6 py-4 border-b">
          <h2 className="text-xl font-semibold text-[#4F2683]">Assign Role</h2>
          <button
            className="w-8 h-8 text-xl bg-[#4F2683] text-white rounded-full flex items-center justify-center"
            type="button"
            onClick={onClose}
          >
            &times;
          </button>
        </div>

        {/* Content Section */}
        <div className="p-6">
          {/* Owner Dropdown */}
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Owner
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={[{ label: "Yes", value: "yes" }, { label: "No", value: "no" }]}
                placeholder="Select Owner"
                value={owner}
                onSelect={setOwner}
              />
            </div>
          </div>

          {/* Approver Level Dropdown */}
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Approver Level
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={[
                  { label: "Level 1", value: "level1" },
                  { label: "Level 2", value: "level2" },
                  { label: "Level 3", value: "level3" },
                ]}
                placeholder="Select Approver Level"
                value={approverLevel}
                onSelect={setApproverLevel}
              />
            </div>
          </div>

          {/* Department Dropdown */}
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Department
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={[
                  { label: "HR", value: "hr" },
                  { label: "Finance", value: "finance" },
                  { label: "IT", value: "it" },
                ]}
                placeholder="Select Department"
                value={department}
                onSelect={setDepartment}
              />
            </div>
          </div>
        </div>

        {/* Footer Section */}
        <div className="flex justify-center px-6 py-4 ">
          <button
            className="px-4 py-2 mr-2 text-gray-700 bg-gray-200 rounded"
            onClick={onClose}
          >
            Cancel
          </button>
          <button className="px-4 py-2 text-white bg-[#4F2683] rounded">
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

export default ViewReport;
