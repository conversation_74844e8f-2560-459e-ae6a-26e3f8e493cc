import React from 'react';

const Corporate = () => {
  const hiringOrganization = {
    Type: 'Employee',
    Status: 'ACTIVE',
    HiringCompany: 'ORACLE AMERICA, INC.',
    HiringOrganization: 'ORCL US',
    StartDate: 'Jan-11-2021',
    LegacyCostCenter: '6EP2 - OCI Physical Security',
  };

  const corporateIdentity = {
    HCMID: '30001161491970',
    UserName: '<EMAIL>',
    OracleID: '*********',
    ManagerSponsor: '<PERSON> *********',
  };

  const facilityInformation = {
    Facility: 'Santa Clara',
  };

  const address = {
    WorkAddressLine1: '4030 George Sellon Circle',
    WorkAddressLine2: '',
    Country: 'United States',
    State: 'CA',
    City: 'Santa Clara',
    PostalCode: '95054',
  };

  // Helper function to format key labels (e.g., facilityName -> Facility Name)
  const formatKey = (key) => {
    const withSpaces = key.replace(/([A-Z])/g, ' $1');
    return withSpaces.charAt(0).toUpperCase() + withSpaces.slice(1).trim();
  };

  // Helper function to render a section with styling
  const renderSection = (title, data) => (
    <div className="bg-white px-4 pb-2 mb-4 p-2 shadow-[0px_3.94px_7.88px_4.93px_#4F26830F] rounded-lg my-6">
      <h3 className="font-poppins text-[14px] text-[#4F2683] font-[500]">{title}</h3>
      <hr className="my-2" />
      <div>
        {Object.entries(data).map(([key, value]) => (
          <div className="flex items-start mb-2" key={key}>
            <div className="w-1/4">
              <p className="text-[#7C7C7C] font-poppins text-[12px]">{formatKey(key)}</p>
            </div>
            <div className="w-3/4">
              <p className="text-[#000] font-[400] font-poppins text-[12px]">
                {value && (typeof value === 'object' ? (value.value || value.label) : value)}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className=''>
      {renderSection('Hiring Organization', hiringOrganization)}
      {renderSection('Corporate Identity', corporateIdentity)}
      {renderSection('Facility Information', facilityInformation)}
      {renderSection('Address', address)}
    </div>
  );
};

export default Corporate;
