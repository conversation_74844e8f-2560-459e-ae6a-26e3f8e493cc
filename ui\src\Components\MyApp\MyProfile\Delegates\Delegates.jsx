import React, { useState, useMemo } from "react";
import GenericTable from "../../../GenericTable";
import AddDelegateModal from "./AddDelegateModal";

const Delegates = () => {
  const [myDelegatesSearchTerm, setMyDelegatesSearchTerm] = useState("");
  const [delegatesToMeSearchTerm, setDelegatesToMeSearchTerm] = useState("");
  const [myDelegatesData, setMyDelegatesData] = useState([
    {
      name: "DAVID STOLLER",
      eid: "422353",
      taskType: "Access Area Audit",
      sendNotificationTo: "Delegate & Owner",
      startDate: "19-Mar-2025",
      endDate: "19-Mar-2025",
    },
    {
      name: "STEVEN KRUSCHE",
      eid: "3545",
      taskType: "Meeting Site",
      sendNotificationTo: "Delegate & Owner",
      startDate: "19-Mar-2025",
      endDate: "19-Mar-2025",
    },
    // ...other rows...
  ]);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const delegatesToMeData = [
    {
      name: "DAVID STOLLER",
      eid: "422353",
      taskType: "Access Area Audit",
      startDate: "19-Mar-2025",
      endDate: "19-Mar-2025",
      status: "Expired",
    },
    {
      name: "STEVEN KRUSCHE",
      eid: "3545",
      taskType: "Meeting Site",
      startDate: "19-Mar-2025",
      endDate: "19-Mar-2025",
      status: "Expired",
    },
    // ...other rows...
  ];

  const myDelegatesColumns = [
    { name: "Name", selector: (row) => row.name, sortable: true },
    { name: "EID", selector: (row) => row.eid },
    { name: "Task Type", selector: (row) => row.taskType },
    { name: "Send Notification To", selector: (row) => row.sendNotificationTo },
    { name: "Start Date", selector: (row) => row.startDate },
    { name: "End Date", selector: (row) => row.endDate },
  ];

  const delegatesToMeColumns = [
    { name: "Name", selector: (row) => row.name, sortable: true },
    { name: "EID", selector: (row) => row.eid },
    { name: "Task Type", selector: (row) => row.taskType },
    { name: "Start Date", selector: (row) => row.startDate },
    { name: "End Date", selector: (row) => row.endDate },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <div
          className={
            row.status === "Valid"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683] px-2 py-1 rounded"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F] px-2 py-1 rounded"
          }
        >
          {row.status}
        </div>
      ),
    },
  ];

  const filteredMyDelegatesData = useMemo(() => {
    if (!myDelegatesSearchTerm) return myDelegatesData;
    return myDelegatesData.filter((item) =>
      Object.values(item).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(myDelegatesSearchTerm.toLowerCase())
      )
    );
  }, [myDelegatesData, myDelegatesSearchTerm]);

  const filteredDelegatesToMeData = useMemo(() => {
    if (!delegatesToMeSearchTerm) return delegatesToMeData;
    return delegatesToMeData.filter((item) =>
      Object.values(item).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(delegatesToMeSearchTerm.toLowerCase())
      )
    );
  }, [delegatesToMeData, delegatesToMeSearchTerm]);

  const handleAddDelegate = (newDelegate) => {
    const {
      searchIdentity: { name, eid },
      taskType,
      sendNotificationTo,
      startDate: rawStart,
      endDate: rawEnd,
    } = newDelegate;

    // helper to convert Date objects to readable strings
    const formatDate = (d: string | Date) =>
      d instanceof Date ? d.toLocaleString() : d;

    const startDate = formatDate(rawStart);
    const endDate = formatDate(rawEnd);

    setMyDelegatesData([
      { name, eid, taskType, sendNotificationTo, startDate, endDate },
      ...myDelegatesData,
    ]);
  };

  return (
    <div>
      <div className="mb-4 bg-white rounded-lg shadow-md max-h-[70vh] overflow-y-auto">
        <GenericTable
          title="My Delegates"
          showAddButton={true}
          onAdd={() => setIsModalOpen(true)}          
          searchTerm={myDelegatesSearchTerm}
          onSearchChange={(e) => setMyDelegatesSearchTerm(e.target.value)}
          columns={myDelegatesColumns}
          data={filteredMyDelegatesData}
          fixedHeader
          fixedHeaderScrollHeight="400px"
          highlightOnHover
          striped
        />
      </div>

      <AddDelegateModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onAdd={handleAddDelegate}
      />

      <div className="bg-white rounded-lg shadow-md">
        <GenericTable
          title="Delegates to Me"
          showAddButton={false}
          searchTerm={delegatesToMeSearchTerm}
          onSearchChange={(e) => setDelegatesToMeSearchTerm(e.target.value)}
          columns={delegatesToMeColumns}
          data={filteredDelegatesToMeData}
          fixedHeader
          fixedHeaderScrollHeight="400px"
          highlightOnHover
          striped
        />
      </div>
    </div>
  );
};

export default Delegates;
