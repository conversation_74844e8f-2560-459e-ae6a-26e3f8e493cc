import React, { useState } from 'react';

const Identity = () => {
  const [profileDetails] = useState({
    FirstName: 'Adam',
    MiddleName: 'LETHLEAN',
    LastName: 'LETHLEAN',
    PreferredFirstName: 'ADAM LETHLEAN',
    EID: '1234',
    PrimaryWorkEmail: '<EMAIL>',
    WorkPhone: '123 - 123456',
    MobilePhone: '1234567890',
  });

  // Helper function to format key labels (e.g., facilityName -> Facility Name)
  const formatKey = (key) => {
    const withSpaces = key.replace(/([A-Z])/g, ' $1');
    return withSpaces.charAt(0).toUpperCase() + withSpaces.slice(1).trim();
  };

  return (
    <div className="">
      <div className="bg-white px-4  pb-2 p-2  mb-2 shadow-[0px_3.94px_7.88px_4.93px_#4F26830F] rounded-lg my-6">
        <h3 className="font-[500] text-[14px] text-[#4F2683] font-poppins">Profile Details</h3>
        <hr className="my-2" />
        <div>
          {Object.entries(profileDetails).map(([key, value]) => (
            <div className="flex items-start mb-2" key={key}>
              <div className="w-1/4">
                <p className="text-[#7C7C7C] font-poppins text-[12px]">{formatKey(key)}</p>
              </div>
              <div className="w-3/4">
                <p className="text-[#000] font-[400] font-poppins text-[12px]">
                  {value && (typeof value === 'object' ? (value.value || value.label) : value)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Identity;
