import React, { useState } from "react";

const AddVehicleModal = ({ onClose, onSave, isOpen }) => {
  const [formData, setFormData] = useState({
    plate: "",
    year: "",
    make: "",
    model: "",
    color: "",
    updatedDate: new Date().toLocaleDateString(),
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  if (!isOpen) return null; // Ensure modal visibility is controlled by isOpen prop

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-3xl bg-white rounded-lg shadow-lg h-full overflow-y-auto">
        <div className="flex items-center mb-2 px-4 pt-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">Add Vehicle</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={onClose}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <form onSubmit={handleSubmit} className="p-6 rounded-lg">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">
            Vehicle Details
          </h2>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Plate
            </label>
            <div className="w-3/4">
              <input
                type="text"
                name="plate"
                value={formData.plate}
                onChange={handleChange}
                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                required
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Year
            </label>
            <div className="w-3/4">
              <input
                type="text"
                name="year"
                value={formData.year}
                onChange={handleChange}
                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                required
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Make
            </label>
            <div className="w-3/4">
              <input
                type="text"
                name="make"
                value={formData.make}
                onChange={handleChange}
                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                required
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Model
            </label>
            <div className="w-3/4">
              <input
                type="text"
                name="model"
                value={formData.model}
                onChange={handleChange}
                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                required
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Color
            </label>
            <div className="w-3/4">
              <input
                type="text"
                name="color"
                value={formData.color}
                onChange={handleChange}
                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                required
              />
            </div>
          </div>
          <div className="flex justify-center gap-4 mt-6">
            <button
              type="button"
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded"
              onClick={onClose}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-[#4F2683] text-white px-4 py-2 rounded"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddVehicleModal;
