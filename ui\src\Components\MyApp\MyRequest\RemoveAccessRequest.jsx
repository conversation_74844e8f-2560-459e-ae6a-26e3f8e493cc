import React, { useState } from "react";
import GenericTable from "../../GenericTable"; // Reuse GenericTable component
import Input from "../../Global/Input/Input";
import DateInput from "../../Global/Input/DateInput";

const RemoveAccessRequest = ({ onClose, onRemoveAccess }) => {
  const [formData, setFormData] = useState({
    justification: "",
    selectedAccessAreas: [],
  });
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const accessAreasData = [
    { areaName: "Area 1", type: "Area", startDate: "2023-03-01", endDate: "2023-03-10", lastused: "Nema", status: "Assigned" },
    { areaName: "Area 2", type: "Area", startDate: "2023-03-05", endDate: "2023-03-15", status: "Assigned" },
  ];

  const accessAreasColumns = [
    {
      name: "Select",
      cell: (row) => (
        <input
          type="checkbox"
          onChange={(e) => {
            const isChecked = e.target.checked;
            setFormData((prev) => {
              const updatedSelected = isChecked
                ? [...prev.selectedAccessAreas, row]
                : prev.selectedAccessAreas.filter((area) => area !== row);
              return { ...prev, selectedAccessAreas: updatedSelected };
            });
          }}
        />
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
    { name: "Access Area", selector: (row) => row.areaName, sortable: true },
    { name: "Type", selector: (row) => row.type },
    { name: "Start Date", selector: (row) => row.startDate },
    { name: "End Date", selector: (row) => row.endDate },
    { name: "Last Used", selector: (row) => row.lastused },
     {
  name: "Status",
  selector: (row) => row.status, // for sorting/filtering
  cell: (row) => (
    <span
      className={`w-20 py-1 flex justify-center items-center rounded-full text-sm font-medium
        ${
          row.status.toLowerCase() === "assigned"
            ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
            : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
        }`}
    >
      {row.status}
    </span>
  ),
}

  ];

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Remove Access Request Submitted:", formData);
    onRemoveAccess(formData);
    onClose();
  };

  const handleClose = () => {
    setFormData({
      justification: "",
      selectedAccessAreas: [],
    });
    if (onClose) {
      onClose(); // Ensure onClose is called
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`w-full max-w-3xl bg-white rounded-lg shadow-lg h-full overflow-y-auto transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center mb-2 px-4 pt-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Remove Access Request
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <form onSubmit={handleSubmit} className="p-6 rounded-lg">
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Activation Date *
            </label>
            <div className="w-3/4">
              <DateInput
                value={formData.activationDate}
                onChange={(date) => handleChange({ target: { name: "activationDate", value: date } })}
                placeholder="Apr-04-2025"
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Justification
            </label>
            <div className="w-3/4">
              <Input
                name="justification"
                type="bubbles"
                placeholder="Select Justification"
                value={formData.justification}
                // height="94px"
                bubbles={true}
                bubbleOptions={[
                  "Lost Permanent Card",
                  "Forgot Permanent Card",
                ]}
                onChange={handleChange}
              />
            </div>
          </div>
          <div className="mb-4">
            <h2 className="text-[20px] text-[#333333] font-medium pb-4">
              Access List
            </h2>
            <GenericTable
              showAddButton={false}
              columns={accessAreasColumns}
              data={accessAreasData}
              fixedHeader
              fixedHeaderScrollHeight="200px"
              highlightOnHover
              striped
              selectableRows
              onSelectedRowsChange={({ selectedRows }) =>
                setFormData({ ...formData, selectedAccessAreas: selectedRows })
              }
            />
          </div>
          <div className="flex justify-center gap-4 mt-6">
            <button
              type="button"
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded"
              onClick={handleClose} // Ensure handleClose is used here
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-[#4F2683] text-white px-4 py-2 rounded"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RemoveAccessRequest;
