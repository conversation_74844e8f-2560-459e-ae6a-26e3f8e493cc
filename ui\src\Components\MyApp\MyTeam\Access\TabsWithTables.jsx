import React, { useState } from "react";
import GenericTable from "../../../GenericTable";

const TabsWithTables = ({ onClose }) => {
  const [activeTab, setActiveTab] = useState("tab1");

  const reportDataTab1 = [
    { name: "<PERSON>", eid: "E12345", company: "ABC Corp", type: "Employee", status: "Active" },
    { name: "<PERSON>", eid: "E67890", company: "XYZ Inc", type: "Contractor", status: "Inactive" },
    // ...other rows...
  ];

  const reportDataTab2 = [
    { areaName: "Main Office", type: "Restricted" },
    { areaName: "Warehouse", type: "Open" },
    // ...other rows...
  ];

  const columnsTab1 = [
    { name: "Name", selector: (row) => row.name, sortable: true },
    { name: "EID", selector: (row) => row.eid, sortable: true },
    { name: "Company", selector: (row) => row.company },
    { name: "Type", selector: (row) => row.type },
    { name: "Status", selector: (row) => row.status },
  ];

  const columnsTab2 = [
    { name: "Area Name", selector: (row) => row.areaName, sortable: true },
    { name: "Type", selector: (row) => row.type },
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-5xl bg-white rounded-lg shadow-lg">
        {/* Header Section */}
        <div className="flex items-center justify-between px-6 py-4 border-b">
          <h2 className="text-2xl font-semibold text-[#4F2683]">View Request</h2>
          <button
            className="w-8 h-8 text-xl bg-[#4F2683] text-white rounded-full flex items-center justify-center"
            type="button"
            onClick={onClose}
          >
            &times;
          </button>
        </div>

        {/* Details Section */}
        <div className="px-6 py-4 border-b">
          <div className="flex flex-col gap-2">
            <div className="flex">
              <p className="text-sm  font-medium text-gray-500 w-1/3">Request ID</p>
              <p className="text-base font-semibold text-gray-800">1234567</p>
            </div>
            <div className="flex">
              <p className="text-sm font-medium text-gray-500 w-1/3">Request Type</p>
              <p className="text-base font-semibold text-gray-800">Request Access</p>
            </div>
            <div className="flex">
              <p className="text-sm font-medium text-gray-500 w-1/3">Request Date</p>
              <p className="text-base font-semibold text-gray-800">18-Mar-2025 11:45 PM</p>
            </div>
            <div className="flex">
              <p className="text-sm font-medium text-gray-500 w-1/3">Request By</p>
              <p className="text-base font-semibold text-gray-800">Adam Leithean</p>
            </div>
            <div className="flex">
              <p className="text-sm font-medium text-gray-500 w-1/3">Access End</p>
              <p className="text-base font-semibold text-gray-800">---</p>
            </div>
            <div className="flex">
              <p className="text-sm font-medium text-gray-500 w-1/3">Justification</p>
              <p className="text-base font-semibold text-gray-800">Lost permanent card</p>
            </div>
          </div>
        </div>

        {/* Tabs Section */}
        <div className="flex space-x-4 pt-4 ps-4 my-3">
          <button
            className={`w-1/6 py-2 rounded-full font-medium ${
              activeTab === "tab1"
                ? "bg-[#4F2683] text-white"
                : "border border-[#4F2683] text-[#4F2683] bg-white"
            }`}
            onClick={() => setActiveTab("tab1")}
          >
            Identity
          </button>
          <button
          className={`w-1/6 py-2 rounded-full font-medium ${
            activeTab === "tab2"
              ? "bg-[#4F2683] text-white"
              : "border border-[#4F2683] text-[#4F2683] bg-white"
          }`} onClick={() => setActiveTab("tab2")}
          >
            Access List
          </button>
        </div>

        {/* Content Section */}
        <div className="bg-white">
          <GenericTable
            showAddButton={false}
            columns={activeTab === "tab1" ? columnsTab1 : columnsTab2}
            data={activeTab === "tab1" ? reportDataTab1 : reportDataTab2}
            fixedHeader
            fixedHeaderScrollHeight="400px"
            highlightOnHover
            striped
          />
        </div>
      </div>
    </div>
  );
};

export default TabsWithTables;
