import React, { useState, useMemo } from "react";
import GenericTable from "../../../../Components/GenericTable";
import AddCardForm from "./AddCardForm";
import ViewCardDetails from "./ViewCardDetails";
import PasswordGeneration from "./PasswordGeneration"; // Import the new component
import viewicon from "../../../../Images/ViewIcon.svg";
import viewcard from "../../../../Images/ViewCard.svg";
import viewhistory from "../../../../Images/ViewHistory.svg";
const Cards = () => {
  const [isAddCardOpen, setIsAddCardOpen] = useState(false);
  const [isViewCardOpen, setIsViewCardOpen] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);
  const [tableSearchTerm, setTableSearchTerm] = useState("");
  const [isPasswordGenerationOpen, setIsPasswordGenerationOpen] = useState(false); // New state

  const [cards, setCards] = useState([
    {
      cardNumber: "995153",
      cardFormat: "Multitech General",
      cardType: "Permanent",
      activation: "Jul - 20 - 2022",
      deactivation: "Jul - 20 - 2032",
      status: "Valid",
    },
    // ...other cards
  ]);

  const columns = [
    {
      name: "Card Number",
      selector: (row) => row.cardNumber,
      sortable: true,
    },
    {
      name: "Card Format",
      selector: (row) => row.cardFormat,
    },
    {
      name: "Card Type",
      selector: (row) => row.cardType,
    },
    {
      name: "Activation",
      selector: (row) => row.activation,
    },
    {
      name: "Deactivation",
      selector: (row) => row.deactivation,
    },
    {
      name: "Status",
      selector: (row) => row.status,
    },
    {
      name: "Action",
      cell: (row) => (

        <>
          <div className="flex justify-center items-center space-x-2">

            <img src={viewicon} alt="View Icon" onClick={() => handleViewCard(row)} className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]" />

            <img src={viewcard} alt="" className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]" />
            <img src={viewhistory} alt="" className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]" onClick={handlePasswordGeneration} /> {/* Attach the handler */}
          </div>
        </>
      ),
    },



  ];

  const handleAddCard = () => setIsAddCardOpen(true);
  const handleViewCard = (card) => {
    setSelectedCard(card);
    setIsViewCardOpen(true);
  };
  const handlePasswordGeneration = () => setIsPasswordGenerationOpen(true); // New handler

  const generateUniqueCardNumber = () => {
    return Date.now().toString();
  };

  const handleAddCardSubmit = (formData) => {
    const newCard = {
      cardNumber: generateUniqueCardNumber(),
      cardFormat: formData.cardFormat,
      cardType: formData.cardType,
      activation: formData.activationDate,
      deactivation: formData.deactivationDate,
      status: "Pending",
    };

    setCards((prevCards) => [...prevCards, newCard]);
    setIsAddCardOpen(false);
  };

  const filteredData = useMemo(() => {
    if (!tableSearchTerm) return cards;
    return cards.filter((card) =>
      Object.values(card).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(tableSearchTerm.toLowerCase())
      )
    );
  }, [cards, tableSearchTerm]);

  return (
    <div>
     
      <div className="bg-white rounded-lg shadow-md">
        <GenericTable
          title={"Cards"}
          searchTerm={tableSearchTerm}
          onSearchChange={(e) => setTableSearchTerm(e.target.value)}
          columns={columns}
          data={filteredData}
          onAdd={handleAddCard}
          fixedHeader
          fixedHeaderScrollHeight="400px"
          highlightOnHover
          striped
        />
      </div>
      {isAddCardOpen && (
        <AddCardForm onClose={() => setIsAddCardOpen(false)} onAdd={handleAddCardSubmit} />
      )}
      {isViewCardOpen && (
        <ViewCardDetails card={selectedCard} onClose={() => setIsViewCardOpen(false)} />
      )}
      {isPasswordGenerationOpen && (
        <PasswordGeneration onClose={() => setIsPasswordGenerationOpen(false)} />
      )}
    </div>
  );
};

export default Cards;
