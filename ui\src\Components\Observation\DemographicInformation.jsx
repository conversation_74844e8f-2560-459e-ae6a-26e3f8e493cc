import React, { useState, useEffect } from 'react';
import EditableSection from '../../Components/Global/EditableSection';
import { updateWatchlist } from '../../api/watchList';
import formatDateTime from "../../utils/formatDateTime";

const DemographicInformation = ({ data, watchlistId  }) => {
    const [biographicData, setBiographicData] = useState({
        FirstName: '',
        MiddleName: '',
        LastName: '',
        suffix: '',
        DateOfBirth: '',
        Status: '',
        HostPatient: '',
        Email: '',
        Phone: '',
        expiryDate: '',
    });

    const [physicalDescriptionData, setPhysicalDescriptionData] = useState({
        Gender: '',
        Height: '',
        Weight: '',
        HairColor: '',
        EyesColor: '',
    });

    const [addressData, setAddressData] = useState('');

    useEffect(() => {
        if (data) {
            setBiographicData({
                FirstName: data.first_name || '',
                MiddleName: data.middle_name || '',
                LastName: data.last_name || '',
                suffix: data.suffix || '',
                 DateOfBirth: data.date_of_birth ? formatDateTime(data.date_of_birth) : '',
                Status: data.status === 0 ? 'Inactive' : 'Active',
                HostPatient: data.host || '',
                Email: data.email || '',
                Phone: data.phone || '',
               expiryDate: data.expiry_date ? formatDateTime(data.expiry_date) : '',
            });

            setPhysicalDescriptionData({
                Gender: data.gender || '',
                Height: data.height || '',
                Weight: data.weight || '',
                HairColor: data.hair_color || '',
                EyesColor: data.eyes_color || '',
            });

            setAddressData(data.address || '');
        }
    }, [data]);

    const handleInputChange = (section, key, value) => {
        if (section === 'biographic') {
            setBiographicData((prev) => ({ ...prev, [key]: value }));
        } else if (section === 'physicalDescription') {
            setPhysicalDescriptionData((prev) => ({ ...prev, [key]: value }));
        } else if (section === 'address') {
            setAddressData(value);
        }
    };
    const statusOptions = [
        { label: "Active", value: "Active" },
        { label: "Inactive", value: "Inactive" },
      ];

    return (
        <div>
          <EditableSection
  title="Biographic"
  data={biographicData}
  dropdownKeys={['Status']}
  dateKeys={['DateOfBirth', 'expiryDate']}
  dropDownclassName="text-[#696969]"
  dropdownOptions={{ Status: statusOptions }}
  onChange={(key, value) => handleInputChange('biographic', key, value)}
  onSave={async (updatedData) => {
    try {
      await updateWatchlist(watchlistId, {
        first_name: updatedData.FirstName,
        middle_name: updatedData.MiddleName,
        last_name: updatedData.LastName,
        suffix: updatedData.suffix,
        date_of_birth: updatedData.DateOfBirth,
        status: updatedData.Status === 'Active' ? 1 : 0,
        host: updatedData.HostPatient,
        email: updatedData.Email,
        phone: updatedData.Phone,
        expiry_date: updatedData.expiryDate,
      });
      console.log("Biographic info updated");
    } catch (error) {
      console.error("Failed to update biographic info", error);
    }
  }}
/>

         <EditableSection
  title="Physical Description"
  data={physicalDescriptionData}
  onChange={(key, value) => handleInputChange('physicalDescription', key, value)}
  onSave={async (updatedData) => {
    try {
      await updateWatchlist(watchlistId, {
        gender: updatedData.Gender,
        height: updatedData.Height,
        weight: updatedData.Weight,
        hair_color: updatedData.HairColor,
        eyes_color: updatedData.EyesColor,
      });
      console.log("Physical description updated");
    } catch (error) {
      console.error("Failed to update physical description", error);
    }
  }}
/>

           <EditableSection
  title="Address"
  data={{ Address: addressData }}
  onChange={(key, value) => handleInputChange('address', key, value)}
  onSave={async (updatedData) => {
    try {
      await updateWatchlist(watchlistId, {
        address: updatedData.Address,
      });
      console.log("Address updated");
    } catch (error) {
      console.error("Failed to update address", error);
    }
  }}
/>

        </div>
    );
};

export default DemographicInformation;
