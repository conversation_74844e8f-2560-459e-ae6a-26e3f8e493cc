import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import GenericTable from '../GenericTable';
import { FaEye, FaTrash } from 'react-icons/fa';
import Button from '../Global/Button';

import {
  createWatchlistDocument,
  getWatchlistDocument,
  
} from '../../api/PatientHub';  
import { getMediaByModel } from '../../api/global';
import {deleteWatchlistDocument} from '../../api/watchList'; // Adjust the import path as necessary
import formatDateTime from "../../utils/formatDateTime";

const MySwal = withReactContent(Swal);

function Document({ watchlistId }) {
  const [documents, setDocuments] = useState([]);

  // Load existing document on mount
  useEffect(() => {
    if (!watchlistId) return;

    (async () => {
      try {
        const serverDocs = await getWatchlistDocument(watchlistId);

        const resolvedDocuments = await Promise.all(
          serverDocs.map(async (doc) => {
            let resolvedURL = doc.url;
            const fileId = doc.documents;
            if (fileId) {
              try {
                const mediaRes = await getMediaByModel(
                  'WatchlistDocument',
                  { key: 'documents', value: fileId }
                );
                resolvedURL = mediaRes.value || mediaRes.data?.value || resolvedURL;
              } catch (err) {
                console.error('Error fetching document media:', err);
              }
            }
            return {
              id: doc.watchlist_document_id,
              name: doc.name,
              file: doc.documents,
              fileURL: resolvedURL,
              fileType: 'application/pdf', // Adjust based on your API response
              date: doc.createdAt,
            };
          })
        );

        setDocuments(resolvedDocuments);
      } catch (err) {
        console.error('Error fetching existing documents:', err);
      }
    })();
  }, [watchlistId]);

  const handleUploadModal = () => {
    let tempSelectedFile = null;
    let tempDocumentName = '';

    const renderModal = () => {
      MySwal.fire({
        title: '<div class="flex font-normal text-[#4F2683]">Upload Document</div>',
        showCloseButton: true,
        html: (
          <div>
            <label className="mb-2 text-sm font-medium text-[#4F2683]">
              Document Name
            </label>
            <input
              type="text"
              placeholder="Enter document name"
              className="block w-full border rounded-md px-3 py-2 mb-4 outline-none"
              defaultValue={tempDocumentName}
              onInput={e => (tempDocumentName = e.target.value)}
            />

            <label className="mb-2 text-sm font-medium text-[#4F2683]">
              Upload File
            </label>
            <div
              className="border-dashed border-2 border-gray-300 rounded-lg h-32 flex items-center justify-center cursor-pointer"
              onClick={() => document.getElementById('fileInput').click()}
            >
              <span className="text-gray-500 text-3xl">+</span>
            </div>
            <input
              type="file"
              id="fileInput"
              style={{ display: 'none' }}
              onChange={e => {
                tempSelectedFile = e.target.files[0];
                renderModal();
              }}
            />

            {tempSelectedFile && (
              <p className="mt-2 text-gray-700">
                Selected File: {tempSelectedFile.name}
              </p>
            )}
          </div>
        ),
        showConfirmButton: false,
        footer: (
          <div className="flex justify-end space-x-2 mt-4">
            <Button type="cancel" label="Cancel" onClick={() => Swal.close()} />
            <Button
              type="primary"
              label="Save"
              onClick={async () => {
                if (!tempDocumentName || !tempSelectedFile) {
                  Swal.showValidationMessage(
                    'Please provide a document name and upload a file.'
                  );
                  return;
                }
                try {
                  // build form data using `image` per your Swagger spec
                  const form = new FormData();
                  form.append('name', tempDocumentName);
                  form.append('image', tempSelectedFile);

                  // POST to /watchlist/{watchlistId}/document
                  const created = await createWatchlistDocument(
                    watchlistId,
                    form
                  );

                  setDocuments([
                    {
                      id:       created.id,
                      name:     created.name,
                      file:     created.fileName,
                      fileURL:  created.url,
                      fileType: created.mimeType,
                      date:     created.uploadedAt,
                    }
                  ]);

                  Swal.close();
                  Swal.fire('Saved!', 'Your document has been uploaded.', 'success');
                } catch (err) {
                  console.error(err);
                  Swal.fire('Error', 'Could not upload document.', 'error');
                }
              }}
            />
          </div>
        ),
      });
    };

    renderModal();
  };

 // Accept the already-resolved document object
  const handleViewDocument = (doc) => {
     console.log("Viewing:", doc.fileURL, doc.fileType);
    MySwal.fire({
      title: `<div class='text-[#4F2683] text-2xl font-normal'>${doc.name}</div>`,
      showCloseButton: true,
      width: '60%',
        html: `
        <div class="mt-4 border h-[500px]">
          <embed 
            src="${doc.fileURL}" 
            width="100%" 
            height="100%" 
          />
        </div>
        <div class="mt-2 text-center">
          <a href="${doc.fileURL}" download class="underline">
            Download File
          </a>
        </div>
      `,
      confirmButtonText: 'OK'
    });
  };

  const handleDeleteDocument = async (docId) => {
    try {
      const confirmation = await MySwal.fire({
        title: 'Are you sure?',
        text: 'You won’t be able to revert this!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel',
      });

      if (confirmation.isConfirmed) {
        await deleteWatchlistDocument(docId);
        setDocuments((prevDocuments) =>
          prevDocuments.filter((doc) => doc.id !== docId)
        );
        Swal.fire('Deleted!', 'The document has been deleted.', 'success');
      }
    } catch (err) {
      console.error('Error deleting document:', err);
      Swal.fire('Error', 'Could not delete the document.', 'error');
    }
  };

  const columns = [
    { name: 'Document Name', selector: row => row.name, sortable: true },
    { name: 'File Name',     selector: row => row.createdAt },
    { 
  name: 'Uploaded Date',  
  selector: row => formatDateTime(row.date) 
},

    {
      name: 'Action',
      cell: row => (
        <div className="flex space-x-2">
          <button onClick={() => handleViewDocument(row)}>
            <div className="p-2 rounded-lg bg-[#F0EDF5]">
              <FaEye className='text-[#4F2683]' />
            </div>
          </button>
          <button onClick={() => handleDeleteDocument(row.id)}>
            <div className="p-2 rounded-lg bg-[#F0EDF5]">
              <FaTrash  className='text-red-600'/>
            </div>
          </button>
        </div>
      )
    }
  ];

  return (
    <div className="bg-white shadow rounded-[10px]">
      <GenericTable
        title="Uploaded Documents"
        showSearch={false}
        showAddButton={false}
        extraControls={
          <Button
            type="primary"
            label="Upload Document"
            onClick={handleUploadModal}
            rounded
          />
        }
        columns={columns}
        data={documents}
        fixedHeader={documents.length > 5}
        fixedHeaderScrollHeight="300px"
        highlightOnHover
      />
    </div>
  );
}

export default Document;
