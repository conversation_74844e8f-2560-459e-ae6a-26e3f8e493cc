import React, { useState, useEffect } from "react";
import { IoClose } from "react-icons/io5";
import Input from "../Global/Input/Input";

const FilterPanel = ({ isOpen, onClose }) => {
    const [mainCategories, setMainCategories] = useState([
        "Lorem Ipsum 1", "Lorem Is 2", "Lorem Ipsum Is 3", "Lorem Ipsum Is 4"
    ]);

    const [filters, setFilters] = useState({
        "Lorem Ipsum 1": {
            "Criminals Filter Data": ["Filter A1", "Filter A2", "Filter A3"],
            "Friends Filter Data": ["Friend A1", "Friend A2"]
        },
        "Lorem Is 2": {
            "Criminals Filter Data": ["Filter B1", "Filter B2"],
            "Friends Filter Data": ["Friend B1", "Friend B2", "Friend B3"]
        },
        "Lorem Ipsum Is 3": {
            "Criminals Filter Data": ["Filter C1", "Filter C2", "Filter C3"],
            "Friends Filter Data": ["Friend C1", "Friend C2"]
        },
        "Lorem Ipsum Is 4": {
            "Criminals Filter Data": ["Filter D1", "Filter D2", "Filter D3"],
            "Friends Filter Data": ["Friend D1", "Friend D2"]
        },
    });

    const [selectedMainCategory, setSelectedMainCategory] = useState(mainCategories[0]);
    const [selectedFilters, setSelectedFilters] = useState({});
    const [searchQuery, setSearchQuery] = useState("");
    const [show, setShow] = useState(false);

    useEffect(() => {
        setSelectedMainCategory(mainCategories[0]);
    }, [mainCategories]);

    useEffect(() => {
        if (isOpen) {
            setTimeout(() => setShow(true), 10);
        } else {
            setShow(false);
        }
    }, [isOpen]);

    const handleFilterToggle = (category, filter) => {
        setSelectedFilters((prev) => {
            const currentFilters = prev[category] || [];
            return {
                ...prev,
                [category]: currentFilters.includes(filter)
                    ? currentFilters.filter((f) => f !== filter)
                    : [...currentFilters, filter],
            };
        });
    };

    const handleApply = () => {
        console.log("Applied Filters:", selectedFilters);
        alert("Filters applied successfully!");
        onClose();
    };

    const filteredFilters = selectedMainCategory ? Object.keys(filters[selectedMainCategory]).reduce((acc, category) => {
        acc[category] = filters[selectedMainCategory][category].filter((filter) =>
            filter.toLowerCase().includes(searchQuery.toLowerCase())
        );
        return acc;
    }, {}) : {};

    return (
        <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50 ${isOpen ? "block" : "hidden"}`}>
            <div
                className={`bg-[#f1eef5]  w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"
                    }`}
                style={{ willChange: "transform" }}
            >
                {/* Header */}
                <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
                    <h2 className="text-2xl font-semibold text-[#4F2683]">Filters</h2>
                    <button
                        className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
                        type="button"
                        onClick={() => {
                            setShow(false);
                            setTimeout(onClose, 700);
                        }}
                    >
                        &times;
                    </button>
                </div>
                <div className="p-6">
                    {/* Filter content */}
                    <div className="p-6 grid grid-cols-3 gap-6 bg-white shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">
                        {/* Column 1: Main Categories */}
                        <div className="border-r border-[#4F2683]/[0.24] pr-4">
                            <h3 className="text-lg font-semibold text-[#4F2683] mb-2">Select Category</h3>
                            {mainCategories.map((category) => (
                                <p
                                    key={category}
                                    className={`cursor-pointer mb-2 p-2 rounded-lg ${selectedMainCategory === category
                                        ? "bg-[#EEE9F2] text-[#4F2683] font-semibold"
                                        : "text-[#7C7C7C] hover:bg-gray-200"
                                        }`}
                                    onClick={() => setSelectedMainCategory(category)}
                                >
                                    {category}
                                </p>
                            ))}
                        </div>

                        {/* Column 2: Filter options */}
                        <div className="border-r border-[#4F2683]/[0.24] pr-4">
                            <Input
                                type="text"
                                placeholder="Search"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                            <div className="mt-4">
                                {Object.keys(filteredFilters).map((category) => (
                                    <div key={category} className="mb-4">
                                        <h3 className="text-[18px] font-semibold text-[#4F2683] mb-2">{category}</h3>
                                        {filteredFilters[category].map((filter) => (
                                            <div key={filter} className="flex items-center mb-2">
                                                <input
                                                    type="checkbox"
                                                    className="mr-2"
                                                    checked={selectedFilters[category]?.includes(filter) || false}
                                                    onChange={() => handleFilterToggle(category, filter)}
                                                />
                                                <label className="text-gray-800">{filter}</label>
                                            </div>
                                        ))}
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Column 3: Selected filters */}
                        <div className="relative">
                            <h3 className="text-lg font-semibold text-[#4F2683] mb-4">Selected Filters</h3>
                            {Object.keys(selectedFilters).map((category) => (
                                <div key={category} className="mb-4">
                                    <h4 className="text-[16px] font-semibold text-[#4F2683] mb-2">{category}</h4>
                                    <div className="flex flex-wrap gap-2">
                                        {selectedFilters[category].map((filter) => (
                                            <span key={filter} className="flex items-center bg-[#EEE9F2] text-[#4F2683] px-2 py-1 rounded-full text-sm">
                                                {filter}
                                                <button
                                                    className="ml-2 text-white rounded-full bg-[#4F2683] px-1.5"
                                                    onClick={() => handleFilterToggle(category, filter)}
                                                >
                                                    &times;
                                                </button>
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            ))}
                            <button
                                className="absolute bottom-0 left-0 w-full bg-[#4F2683] text-white py-3 rounded-lg hover:bg-[#6A3BAA]"
                                onClick={handleApply}
                            >
                                Apply
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );

};

export default FilterPanel;
