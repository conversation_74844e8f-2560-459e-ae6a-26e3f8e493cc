import React, { useState } from 'react';
import DataTable from 'react-data-table-component';
import { FaEdit } from 'react-icons/fa';
import { IoFilter } from 'react-icons/io5';
import Input from '../Global/Input'; // Adjust import path as needed
import Button from '../Global/Button'; // Adjust import path as needed

const NameCell = ({ row }) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => {
    console.log('Hover entered on:', row.name);
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    console.log('Hover left on:', row.name);
    setIsHovered(false);
  };

  return (
    <div
      className="flex items-center space-x-3"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{ pointerEvents: 'auto' }}
    >
      <img src={row.image} alt={row.name} className="w-8 h-8 rounded-full" />
      <span>{row.name}</span>
      {isHovered && (
        <FaEdit
          className="text-blue-500 cursor-pointer ml-2"
          onClick={() => window.open(`/details`, '_blank')}
        />
      )}
    </div>
  );
};

const TableComponent = ({
  data,
  title,
  highlightOnHover = true,
  striped = false,
  selectableRows = false,
  onRowSelected = () => {},
  // New props for header controls
  searchTerm,
  setSearchTerm,
  onFilterOpen,
  onAddWatchlist,
}) => {
  const columns = [
    {
      name: 'Name',
      selector: (row) => row.name,
      sortable: true,
      cell: (row) => <NameCell row={row} />,
    },
    { name: 'Added By', selector: (row) => row.addedBy, sortable: true },
    { name: 'Added On', selector: (row) => row.addedOn, sortable: true },
    { name: 'Expiration Date', selector: (row) => row.expirationDate, sortable: true },
    {
      name: 'Status',
      selector: (row) => row.status,
      sortable: true,
      cell: (row) => (
        <span
          className={`w-20 py-1 items-center flex justify-center text-sm font-semibold rounded-full ${
            row.status.toLowerCase() === 'active'
              ? 'bg-[#4F268314] bg-opacity-8 text-[#4F2683]'
              : 'bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]'
          }`}
        >
          {row.status}
        </span>
      ),
    },
  ];

  
  const reversedData = [...data].reverse();

  // Enable scrolling if more than 5 rows
  const enableScroll = reversedData && reversedData.length > 5;
  const scrollHeight = '300px';

  return (
    <div className="shadow-[0px_3.94px_7.88px_4.93px_#4F26830F] rounded-[10px] p-4">
      {/* Header Section (moved from Observation) */}
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-normal text-[#4F2683]">Observation Data Table</h3>
        <div className="flex mr-4 items-center">
          <label htmlFor="search" className="mr-2 font-normal text-[18px] text-gray-700">
            Search:
          </label>
          <Input
            id="search"
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <IoFilter
            className="bg-white shadow-[0px_3.94px_7.88px_4.93px_#4F26830F] p-2 text-[#4F2683] h-10 w-14 rounded m-2 mr-4 cursor-pointer"
            onClick={onFilterOpen}
          />
          <Button
            type="primary"
            className="px-[36px] py-2 font-normal whitespace-nowrap"
            rounded={true}
            onClick={onAddWatchlist}
            label="Add"
          />
        </div>
      </div>

      {title && (
        <h2 className="text-xl font-semibold mb-4 text-gray-700">{title}</h2>
      )}

      <DataTable
        columns={columns}
        data={reversedData}
        highlightOnHover={highlightOnHover}
        striped={striped}
        selectableRows={selectableRows}
        onSelectedRowsChange={onRowSelected}
        className="w-full"
        fixedHeader={enableScroll}
        fixedHeaderScrollHeight={enableScroll ? scrollHeight : undefined}
        customStyles={{
          rows: {
            style: {
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: '#f1f5f9',
              },
            },
          },
          headCells: {
            style: {
              fontWeight: 500,
              fontSize: '14px',
              color: '#9971CB',
            },
          },
        }}
      />
    </div>
  );
};

export default TableComponent;
