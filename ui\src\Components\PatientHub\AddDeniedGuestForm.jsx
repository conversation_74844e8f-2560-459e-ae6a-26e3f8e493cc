import React from "react";
import Button from "../Global/Button";
import Input from "../Global/Input/Input";
import DateInput from "../Global/Input/DateInput";
const AddDeniedGuestForm = ({ newEntry, setNewEntry, onSave, onClose }) => {
  // Animation state
  const [show, setShow] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleFormSubmit = (e) => {
    e.preventDefault(); // Prevent the default form submission behavior
    onSave();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-white p-6 rounded shadow-lg w-full max-w-3xl h-full overflow-y-auto transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-[30px] font-normal text-[#4F2683]">Add Denied Guest</h3>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <hr className="mb-4" />
        <form className="bg-white p-2 rounded-lg" onSubmit={handleFormSubmit}>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              First Name*
            </label>
            <div className="w-3/4">
              <Input
                className="border p-2 w-full"
                placeholder="First Name"
                value={newEntry.firstName}
                onChange={(e) =>
                  setNewEntry({ ...newEntry, firstName: e.target.value })
                }
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Last Name*
            </label>
            <div className="w-3/4">
              <Input
                className="border p-2 w-full"
                placeholder="last Name"
                value={newEntry.lastName}
                onChange={(e) =>
                  setNewEntry({ ...newEntry, lastName: e.target.value })
                }
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Date Of Birth
            </label>
            <div className="w-3/4">
              <DateInput
                className="w-full rounded-sm"
                placeholder="YYYY-MM-DD"
                value={newEntry.dateDenied}
                onChange={(date) => setNewEntry({ ...newEntry, dateDenied: date })}
              />
            </div>
          </div>

          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Email
            </label>
            <div className="w-3/4">
              <Input
                className="border p-2 w-full"
                placeholder="Email"
                type="email"
                value={newEntry.contactEmail}
                onChange={(e) =>
                  setNewEntry({ ...newEntry, contactEmail: e.target.value })
                }
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Phone
            </label>
            <div className="w-3/4">
              <Input
                className="border p-2 w-full"
                placeholder="Number"
                type="tel"
                value={newEntry.phone}
                onChange={(e) =>
                  setNewEntry({ ...newEntry, phone: e.target.value })
                }
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Denial Reason *
            </label>
            <div className="w-3/4">
              <Input
                className="border p-2 w-full"
                placeholder="Denial Reason"
                value={newEntry.denialReason}
                onChange={(e) =>
                  setNewEntry({ ...newEntry, denialReason: e.target.value })
                }
              />
            </div>
          </div>
          <div className="flex justify-center gap-4">
            <Button
              type="cancel"
              onClick={onClose}
              label="Cancel"
            />
            <Button
              type="primary"
              label="Save"
            // onClick={handleFormSubmit}yy
            />
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddDeniedGuestForm;