import React, { useState, useEffect } from "react";
import Button from "../Global/Button";

const AddDeviceGroupModal = ({ isOpen, onClose, onSave }) => {
  const [newEntry, setNewEntry] = useState({
    deviceGroup: "",
    assignApp: "",
    status: "Active",
  });
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => setShow(true), 10);
    } else {
      setShow(false);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleClose = () => {
    setShow(false);
    setTimeout(onClose, 700);
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();
    const newDeviceGroup = {
      id: Date.now(),
      ...newEntry,
    };
    onSave(newDeviceGroup);
    setNewEntry({ deviceGroup: "", assignApp: "", status: "Active" });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-white p-6 rounded shadow-lg w-full max-w-3xl h-full overflow-y-auto transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-[30px] font-normal text-[#4F2683]">Add Device Group</h3>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={handleClose}
          >
            &times;
          </button>
        </div>
        <hr className="mb-4" />
        <form className="bg-white p-2 rounded-lg" onSubmit={handleFormSubmit}>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Device Group*
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded"
                placeholder="Device Group"
                value={newEntry.deviceGroup}
                onChange={e => setNewEntry({ ...newEntry, deviceGroup: e.target.value })}
                required
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Assign App*
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded"
                placeholder="Assign App"
                value={newEntry.assignApp}
                onChange={e => setNewEntry({ ...newEntry, assignApp: e.target.value })}
                required
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Status
            </label>
            <div className="w-3/4">
              <select
                className="border p-2 w-full rounded"
                value={newEntry.status}
                onChange={e => setNewEntry({ ...newEntry, status: e.target.value })}
              >
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
              </select>
            </div>
          </div>
          <div className="flex justify-center gap-4">
            <Button
              type="cancel"
              onClick={handleClose}
              label="Cancel"
            />
            <Button
              type="primary"
              label="Save"
            />
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddDeviceGroupModal;
