import React from "react";
import GenericTable from "../GenericTable";
import Button from "../Global/Button";

const PatientHistoryModal = ({ onClose, historyData }) => {
  const columns = [
    { name: "Effective Date", selector: (row) => row.effective_date, width: "200px" },
    { name: "Patient History ID", selector: (row) => row.patient_history_id },
    { name: "Field Changes", selector: (row) => row.field_changes },
    { name: "Old Value", selector: (row) => row.old_value },
    { name: "New Value", selector: (row) => row.new_value },
    { name: "Event Type", selector: (row) => row.event_type },
    { name: "Patient ID", selector: (row) => row.patient_id },
    { name: "Modified By", selector: (row) => row.modified_by },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
      <div className="bg-white p-6 w-[80%] rounded shadow-lg">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-[24px] font-semibold text-[#4F2683]">Patient History</h3>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            onClick={onClose}
          >
            &times;
          </button>
        </div>
        
        <GenericTable
          title="Patient History"
          className="shadow-none"
          columns={columns}
          data={historyData}
          showSearch={false}
          showAddButton={false}
        />
        <div className="flex justify-end mt-4">
          <Button type="cancel" label="Close" onClick={onClose} />
        </div>
      </div>
    </div>
  );
};

export default PatientHistoryModal;
