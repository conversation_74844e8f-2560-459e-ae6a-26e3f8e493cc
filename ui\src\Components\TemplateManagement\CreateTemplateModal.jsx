import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { toast } from 'react-toastify';
import { createBadgeTemplate, getSchemas, getSchemaKeys } from '../../api/badge';

const CreateTemplateModal = ({ open, onClose, onSuccess }) => {
  const navigate = useNavigate();
  const [templateName, setTemplateName] = useState('');
  const [selectedSchema, setSelectedSchema] = useState('');
  const [selectedKey, setSelectedKey] = useState('');
  const [selectedFormat, setSelectedFormat] = useState('');
  const [schemas, setSchemas] = useState([]);
  const [keys, setKeys] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingSchemas, setLoadingSchemas] = useState(false);
  const [loadingKeys, setLoadingKeys] = useState(false);
  const [error, setError] = useState('');

  const formats = [
    { value: 'card', label: 'Card' },
    { value: 'badge', label: 'Badge' },
    { value: 'sticker', label: 'Sticker' },
    { value: 'label', label: 'Label' }
  ];

  // Load schemas on component mount
  useEffect(() => {
    if (open) {
      loadSchemas();
    }
  }, [open]);

  // Load keys when schema changes
  useEffect(() => {
    if (selectedSchema) {
      loadKeys();
    } else {
      setKeys([]);
      setSelectedKey('');
    }
  }, [selectedSchema]);

  const loadSchemas = async () => {
    setLoadingSchemas(true);
    try {
      const response = await getSchemas();
      setSchemas(response?.data?.schemas || []);
    } catch (error) {
      console.error('Error loading schemas:', error);
      toast.error('Failed to load schemas');
    } finally {
      setLoadingSchemas(false);
    }
  };

  const loadKeys = async () => {
    setLoadingKeys(true);
    try {
      const response = await getSchemaKeys(selectedSchema);
      setKeys(response.data || []);
    } catch (error) {
      console.error('Error loading keys:', error);
      toast.error('Failed to load keys');
    } finally {
      setLoadingKeys(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setTemplateName('');
      setSelectedSchema('');
      setSelectedKey('');
      setSelectedFormat('');
      setError('');
      onClose();
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validation
    if (!templateName.trim()) {
      setError('Template name is required');
      return;
    }

    if (templateName.trim().length < 3) {
      setError('Template name must be at least 3 characters long');
      return;
    }

    if (templateName.trim().length > 100) {
      setError('Template name must be less than 100 characters');
      return;
    }

    if (!selectedSchema) {
      setError('Schema is required');
      return;
    }

    if (!selectedKey) {
      setError('Key is required');
      return;
    }

    if (!selectedFormat) {
      setError('Format is required');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const templateData = {
        name: templateName.trim(),
        schema: selectedSchema,
        key: selectedKey,
        format: selectedFormat,
        content: {
          canvasConfig: {},
          elements: []
        }
      };

      const response = await createBadgeTemplate(templateData);

      toast.success('Template created successfully');

      // Navigate to the designer with the new template ID
      navigate(`/id-designer/${response.data.badge_id}`);
      
      // Close modal and refresh parent
      handleClose();
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error creating template:', error);
      
      // Handle specific error messages
      if (error.response?.data?.message) {
        setError(error.response.data.message);
      } else if (error.response?.status === 409) {
        setError('A template with this name already exists');
      } else if (error.response?.status === 400) {
        setError('Invalid template data provided');
      } else {
        setError('Failed to create template. Please try again.');
      }
      
      toast.error('Failed to create template');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 20px 25px -5px rgba(79, 38, 131, 0.1), 0 10px 10px -5px rgba(79, 38, 131, 0.04)'
        }
      }}
    >
      <form onSubmit={handleSubmit}>
        <DialogTitle sx={{
          pb: 1,
          fontSize: '20px',
          fontWeight: 600,
          color: '#4F2683'
        }}>
          Create New Badge Template
        </DialogTitle>
        
        <DialogContent sx={{ pt: 2 }}>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ mb: 3 }}
          >
            Create a new badge template by providing the required information below.
          </Typography>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              autoFocus
              label="Template Name"
              placeholder="e.g., Employee Badge, Visitor Pass, Contractor ID"
              value={templateName}
              onChange={(e) => {
                setTemplateName(e.target.value);
                if (error) setError('');
              }}
              error={!!error && error.includes('name')}
              helperText={error && error.includes('name') ? error : 'Enter a descriptive name for your template'}
              fullWidth
              variant="outlined"
              disabled={loading}
              inputProps={{
                maxLength: 100
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '&:hover fieldset': {
                    borderColor: '#4F2683',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#4F2683',
                  },
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: '#4F2683',
                }
              }}
            />

            <FormControl
              fullWidth
              variant="outlined"
              disabled={loading || loadingSchemas}
              error={!!error && error.includes('Schema')}
            >
              <InputLabel>Schema</InputLabel>
              <Select
                value={selectedSchema}
                onChange={(e) => {
                  setSelectedSchema(e.target.value);
                  if (error) setError('');
                }}
                label="Schema"
              >
                {schemas.map((schema) => (
                  <MenuItem key={schema} value={schema}>
                    {schema}
                  </MenuItem>
                ))}
              </Select>
              {error && error.includes('Schema') && (
                <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                  {error}
                </Typography>
              )}
            </FormControl>

            <FormControl
              fullWidth
              variant="outlined"
              disabled={loading || loadingKeys || !selectedSchema}
              error={!!error && error.includes('Key')}
            >
              <InputLabel>Key</InputLabel>
              <Select
                value={selectedKey}
                onChange={(e) => {
                  setSelectedKey(e.target.value);
                  if (error) setError('');
                }}
                label="Key"
              >
                {keys.map((key) => (
                  <MenuItem key={key.name || key.id} value={key.name || key.id}>
                    {key.display_name || key.name || key.id}
                  </MenuItem>
                ))}
              </Select>
              {error && error.includes('Key') && (
                <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                  {error}
                </Typography>
              )}
            </FormControl>

            <FormControl
              fullWidth
              variant="outlined"
              disabled={loading}
              error={!!error && error.includes('Format')}
            >
              <InputLabel>Format</InputLabel>
              <Select
                value={selectedFormat}
                onChange={(e) => {
                  setSelectedFormat(e.target.value);
                  if (error) setError('');
                }}
                label="Format"
              >
                {formats.map((format) => (
                  <MenuItem key={format.value} value={format.value}>
                    {format.label}
                  </MenuItem>
                ))}
              </Select>
              {error && error.includes('Format') && (
                <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                  {error}
                </Typography>
              )}
            </FormControl>
          </Box>
          
          <Box sx={{ mt: 2 }}>
            <Typography variant="caption" color="text.secondary">
              {templateName.length}/100 characters
            </Typography>
          </Box>
        </DialogContent>
        
        <DialogActions sx={{ px: 3, pb: 3, pt: 1, gap: 1 }}>
          <Button
            onClick={handleClose}
            disabled={loading}
            sx={{
              textTransform: 'none',
              color: '#6b7280',
              '&:hover': {
                backgroundColor: 'rgba(79, 38, 131, 0.04)',
              }
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading || !templateName.trim() || !selectedSchema || !selectedKey || !selectedFormat}
            sx={{
              backgroundColor: '#4F2683',
              '&:hover': {
                backgroundColor: '#3d1f66',
              },
              '&:disabled': {
                backgroundColor: 'rgba(79, 38, 131, 0.3)',
                color: '#6b7280'
              },
              textTransform: 'none',
              fontWeight: 500,
              px: 3,
              minWidth: 120
            }}
          >
            {loading ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CircularProgress size={16} color="inherit" />
                Creating...
              </Box>
            ) : (
              'Create & Edit'
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default CreateTemplateModal;
