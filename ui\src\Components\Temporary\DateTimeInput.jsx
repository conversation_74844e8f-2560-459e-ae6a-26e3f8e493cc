import React, { useState, useRef, useEffect } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./DateTimeInput.css";
import { FaRegCalendarAlt } from "react-icons/fa";

const DateTimeInput = ({ label, value, onChange, placeholder, error, className }) => {
  const [openPicker, setOpenPicker] = useState(false);
  const pickerRef = useRef(null);

  // Handle the date/time change
  const handleChange = (date) => {
    onChange(date);
    setOpenPicker(false);
  };

  // Close the picker if clicking outside
  useEffect(() => {
    const handleEsc = (e) => {
      if (e.key === "Escape") setOpenPicker(false);
    };
    document.addEventListener("keydown", handleEsc);
    return () => document.removeEventListener("keydown", handleEsc);
  }, []);
  
  // Format the displayed value
  const formattedValue = value
    ? value.toLocaleString("en-US", {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
    : "";

  return (
    <div className={className}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
     <div className={`relative`} ref={pickerRef}>
  <input
    type="text"
    readOnly
    value={formattedValue}
    placeholder={placeholder || "Select date & time"}
    className="p-2 w-full border rounded focus:outline-none cursor-pointer focus:ring-1"
    onClick={() => setOpenPicker(true)}
  />
  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
    <FaRegCalendarAlt
      className="h-5 w-5 text-gray-400 cursor-pointer"
      onClick={() => setOpenPicker(true)}
    />
  </div>
  {openPicker && (
    <div className="absolute z-10 mt-2">
      <DatePicker
        inline
        selected={value ? new Date(value) : null}
        onChange={handleChange}
        onSelect={handleChange}
        showTimeSelect
        timeIntervals={15}
        timeCaption="Time"
        dateFormat="MMM-dd-yyyy h:mm aa"
        onClickOutside={() => setOpenPicker(false)} 
      />
    </div>
  )}
</div>

      {error && <div className="text-red-500 text-sm">{error}</div>}
    </div>
  );
}

export default DateTimeInput;
