.time-input-wrapper {
  position: relative;
  width: 200px;
  font-family: Arial, sans-serif;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.time-input {
  width: 100%;
  padding: 10px 35px 10px 15px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}
.time-section{
    display: flex !important;
    height: 200px !important;
    background: #fff !important;
}

.time-option {
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 4px;
}

.time-option:hover {
  background-color: #f0f0f0;
}

.time-option.selected {
  background-color: red;
  color: white;
}
.time-section .hours-list,
.time-section .minutes-list {
    overflow-y: auto;
}
.clock-icon {
  position: absolute;
  right: 10px;
  cursor: pointer;
  font-size: 20px;
}

.time-picker-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  z-index: 1000;
  margin-top: 5px;
}

.time-section {
  display: flex;
  max-height: 200px;
}

.hours-list, .minutes-list {
  width: 50%;
  padding: 10px;
  box-sizing: border-box;
  overflow-y: auto;
  max-height: 200px;
}

.hours-list {
  border-right: 1px solid #ddd;
}

.time-section-header {
  font-size: 14px;
  font-weight: bold;
  margin: 0 0 10px 0;
  color: #333;
  position: sticky;
  top: 0;
  background: white;
  padding: 5px;
}

.time-option {
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 4px;
  margin: 2px 0;
  text-align: center;
}

.time-option:hover {
  background: #f0f0f0;
}

.time-option.selected {
  background: #ffd700;
  color: white;
  font-weight: bold;
}