
import React, {useEffect , useRef, useState } from "react";
import { Tooltip } from "react-tooltip";

const TruncatedRow = ({ text }) => {
    const rowRef = useRef(null);
    const [isOverflowing, setIsOverflowing] = useState(false);
  
    useEffect(() => {
      if (rowRef.current) {
        setIsOverflowing(rowRef.current.scrollWidth > rowRef.current.clientWidth);
      }
    }, [text]);
  
    return (
      <div
        ref={rowRef}
        className="truncate max-w-[120px] overflow-hidden whitespace-nowrap"
        data-tooltip-id={isOverflowing ? `tooltip-${text}` : undefined}
        data-tooltip-content={isOverflowing ? text : undefined}
      >
        {text}
        {isOverflowing &&
          <div className="fixed">
            <Tooltip id={`tooltip-${text}`} className=" !p-2 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium" place="bottom" effect="solid" />
          </div>
        }
      </div>
    );
  };

  export default TruncatedRow