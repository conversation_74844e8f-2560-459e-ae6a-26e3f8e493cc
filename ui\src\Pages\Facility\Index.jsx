import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import GenericTable, { FilterButtons } from "../../Components/GenericTable.jsx";
import FacilityModal from "../../Components/Facility/FacilityModal.jsx";
import FilterPanel from "../../Components/Observation/FilterPanel.jsx";
import newWindow from "../../Images/new-window.svg";
import { getFacilities } from "../../api/facility.js";
import Loader from "../../Components/Loader.jsx";
import { toast } from "react-toastify";

const FacilityTable = () => {
  const { t } = useTranslation();
  const [data, setData] = useState([]); // Facility data
  const [showModal, setShowModal] = useState(false); // Modal visibility
  const [searchTerm, setSearchTerm] = useState(""); // Search term
  const [filter, setFilter] = useState("All"); // Filter state
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false); // Filter panel visibility
  const [hoveredRow, setHoveredRow] = useState(null); // Hovered row index
  const [loading, setLoading] = useState(false); // Loading state

  // Fetch facilities from the API
  const fetchFacilities = useCallback(async () => {
    setLoading(true);
    try {
      const res = await getFacilities();
      setData(res.data.data); // Update facility data
    } catch (error) {
      toast.error("Error fetching facilities:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch facilities on component mount
  useEffect(() => {
    fetchFacilities();
  }, [fetchFacilities]);


  
  // Filtered data based on search term and filter
  const filteredData = useMemo(() => {
    let filtered = data.filter((facility) =>
      facility.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    if (filter === "Active") {
      filtered = filtered.filter(
        (facility) =>
          facility.facility_status_name &&
          facility.facility_status_name.value.toLowerCase() === "active"
      );
    }
    return filtered;
  }, [data, searchTerm, filter]);

  // Table columns
  const columns = useMemo(
    () => [
      {
        name: t("facility.name"),
        selector: (row) => row.name,
        sortable: true,
        cell: (row, index) => (
          <div
            className="relative flex items-center space-x-3 cursor-pointer group w-full"
            onMouseEnter={() => setHoveredRow(index)}
            onMouseLeave={() => setHoveredRow(null)}
            onClick={() => window.open(`/facility/${row.facility_id}`, "_blank")}
          >
            <span>{row.name}</span>
            {hoveredRow === index && (
              <img className="w-4 h-4" src={newWindow} alt={t("facility.open_in_new_window")} />
            )}
          </div>
        ),
      },
      {
        name: t("facility.address"),
        selector: (row) => row.address?.address_line_1 || "",
        center: true,
      },
      {
        name: t("facility.state_province"),
        selector: (row) => row.address?.state?.name || "",
        center: true,
      },
      {
        name: t("facility.country"),
        selector: (row) => row.address?.country?.name || "",
        center: true,
      },
      {
        name: t("facility.status"),
        selector: (row) =>
          row.facility_status_name ? row.facility_status_name.value : row.status,
        cell: (row) => {
          const statusText =
            row.facility_status_name && typeof row.facility_status_name.value === "string"
              ? row.facility_status_name.value
              : row.status || "";
          return (
            <span
              className={`w-20 py-1 flex justify-center items-center text-sm font-semibold rounded-full ${
                statusText &&
                (statusText.toLowerCase() === "active"
                  ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
                  : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]")
              }`}
            >
              {t(`facility.status_${statusText?.toLowerCase()}`) || statusText}
            </span>
          );
        },
        center: true,
      },
    ],
    [hoveredRow, t]
  );

  return (
    <div className="p-6 bg-white pl-20 pt-20">
      <div className="mb-6">
        <h2 className="font-normal text-[24px] mb-2 text-[#4F2683]">{t("facility.title")}</h2>
        <div className="flex justify-start mb-4">
          <FilterButtons
            filter={filter}
            onFilterChange={setFilter}
            filterOptions={[
              { label: t("facility.filter_all"), value: "All" },
              { label: t("facility.filter_active"), value: "Active" },
            ]}
          />
        </div>
        {loading ? (
          <Loader />
        ) : (
          <GenericTable
            title={t("facility.title")}
            searchTerm={searchTerm}
            onSearchChange={(e) => setSearchTerm(e.target.value)}
            onAdd={() => setShowModal(true)} // Show modal on "Add" button click
            columns={columns}
            data={[...filteredData].reverse()}
            fixedHeader
            fixedHeaderScrollHeight="480px"
          />
        )}
      </div>

      {/* Facility Modal */}
      {showModal && (
        <FacilityModal
          onClose={() => setShowModal(false)} // Close modal
          fetchFacilities={fetchFacilities} // Refresh table data after adding a facility
        />
      )}

      {/* Filter Panel */}
      <FilterPanel isOpen={isFilterPanelOpen} onClose={() => setIsFilterPanelOpen(false)} />
    </div>
  );
};

export default FacilityTable;