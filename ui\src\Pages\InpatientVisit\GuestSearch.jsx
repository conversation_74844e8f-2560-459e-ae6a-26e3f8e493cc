import React from "react";
import { useTranslation } from 'react-i18next';
import SearchBar from "../../Components/Global/SearchBar";
import demo from "../../Images/demoimg.svg";

const GuestSearch = ({
  placeholder,
  searchTerm,
  onInputChange,
  results,
  onResultClick,
  isDropdownVisible,
  containerRef,
  onClick,
  onCreateClick,
}) => {
  const { t } = useTranslation();

  return (
    <div className="w-full sm:w-auto relative" ref={containerRef}>
      <SearchBar
        placeholder={placeholder}
        iconSrc={""}
        onInputChange={onInputChange}
        onClick={onClick}
        value={searchTerm}
        borderColor="#4F2683"
      />
      {isDropdownVisible && (
        <div
          className="w-full mt-2 border absolute p-2 bg-white rounded-md shadow-lg overflow-y-auto"
          style={{ maxHeight: "200px" }}
        >
          {results.length > 0 ? (
            results.map((guest) => (
              <div
                key={guest.id || guest.appointment_guest_id}
                className="flex items-center gap-2 p-2 border-b cursor-pointer hover:bg-gray-100"
                onClick={() => onResultClick(guest)}
              >
                <img
                  src={guest.image || demo}
                  alt={`${guest.first_name || t('inpatient_visit.unknown')} ${guest.last_name || t('inpatient_visit.guest')}`}
                  className="w-10 h-10 rounded-full"
                />
                <div>
                  <h2 className="font-semibold">
                    {guest.guestName}
                  </h2>
                  {guest.patientName && (
                    <p className="text-[12px] text-gray-600">
                      {guest.patientName}, MRN: {guest.mrn}
                    </p>
                  )}
                </div>
              </div>
            ))
          ) : (
            <button
              className="text-[#4F2683] "
              onClick={onCreateClick}
            >
              {t('inpatient_visit.create_new_visit')}
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default GuestSearch;
