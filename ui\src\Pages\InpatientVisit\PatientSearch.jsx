import React from "react";
import { useTranslation } from 'react-i18next';
import SearchBar from "../../Components/Global/SearchBar";
import demoimg from '../../Images/demoimg.svg'; 
import formatDateTime from "../../utils/formatDate"; // Import the utility

const PatientSearch = ({
  placeholder,
  searchTerm,
  onInputChange,
  onSearchSubmit,
  results,
  onResultClick,
  isDropdownVisible,
  containerRef,
}) => {
  const { t } = useTranslation();

  return (
    <div className="w-full sm:w-auto relative" ref={containerRef}>
      <form onSubmit={onSearchSubmit}>
        <SearchBar
          placeholder={placeholder}
          iconSrc={""}
          onInputChange={onInputChange}
          value={searchTerm}
          borderColor="#4F2683"
        />
      </form>
      {isDropdownVisible && (
        <div
          className="w-full mt-2 border absolute p-2 bg-white z-10 rounded-md shadow-lg overflow-y-auto"
          style={{ maxHeight: "200px" }}
        >
          {results.map((appointment) => (
            <div
              key={appointment.appointment_id}
              className="flex items-center gap-2 p-2 border-b cursor-pointer hover:bg-gray-100"
              onClick={() => onResultClick(appointment)}
            >
              <img
                src={appointment.image || demoimg}
                alt={appointment.patient_name}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <h2 className="font-semibold">{appointment.patient_name}</h2>
                <div className="flex flex-row gap-1">
                  <p className="text-[12px] text-gray-600">
                    {formatDateTime(appointment.birth_date)}, MRN: {appointment.mrn}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default PatientSearch;
