import React from "react";

const TimeInput = ({ label, name, value, onChange, onBlur }) => {
  return (
    <div>
      {label && (
        <label htmlFor={name} className="block text-sm font-medium mb-1">
          {label}
        </label>
      )}
      <input
        type="time"
        id={name}
        name={name}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        className="w-full border p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
      />
    </div>
  );
};

export default TimeInput;
