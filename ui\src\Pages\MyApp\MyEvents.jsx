import React, { useState, useMemo, useEffect } from "react";
import moment from "moment";
import { useTranslation } from 'react-i18next';
import GenericTable from "../../Components/GenericTable";
import MyVisitsEdit from "../../Components/MyApp/MyVisitsEdit";
import { FilterButtons } from "../../Components/GenericTable";
import Edit from "../../Images/editPen.svg";
import Block from "../../Images/Block.svg";
import View from "../../Images/ViewIcon.svg";
import Clone from "../../Images/Clone.svg";
import TruncatedCell from "../../Components/Tooltip/TruncatedCell";
import TruncatedRow from "../../Components/Tooltip/TrucantedRow";
import AddVisitForm from "../../Components/MyApp/MyVisit/AddVisitForm";
import { getEvents } from "../../api/event"; // Import the API function
import { createEvent } from "../../api/event";
const MyEvents = () => {
  const { t } = useTranslation();

  const columns = (onEdit, onDelete) => [
    {
      name: <TruncatedCell text={t('my_events.event_title')} />,
      selector: (row) => row.title,
      cell: (row) => <TruncatedRow text={row.title} />
    },
    {
      name: <TruncatedCell text={t('my_events.type')} />,
      selector: (row) => row.type,
      cell: (row) => <TruncatedRow text={row.type} />
    },
    {
      name: <TruncatedCell text={t('my_events.category')} />,
      selector: (row) => row.category,
      cell: (row) => <TruncatedRow text={row.category} />
    },
    {
      name: <TruncatedCell text={t('my_events.host')} />,
      selector: (row) => row.host,
      cell: (row) => <TruncatedRow text={row.host} />
    },
    {
      name: <TruncatedCell text={t('my_events.escort')} />,
      selector: (row) => row.escort,
      cell: (row) => <TruncatedRow text={row.escort} />
    },
    {
      name: <TruncatedCell text={t('my_events.start_date_time')} />,
      selector: (row) => row.startDate,
      cell: (row) => <TruncatedRow text={row.startDate} />
    },
    {
      name: <TruncatedCell text={t('my_events.end_date_time')} />,
      selector: (row) => row.endDate,
      cell: (row) => <TruncatedRow text={row.endDate} />
    },
    {
      name: t('my_events.status'),
      selector: (row) => row.status,
      cell: (row) => (
        <span className="px-3 py-1 rounded-full bg-opacity-8 bg-[#4F268314] text-[#4F2683]">
          {t(`my_events.status_${row.status.toLowerCase()}`)}
        </span>
      ), center: true,
    },
    {
      name: t('my_events.actions'),
      cell: (row) => (
        <div className="flex space-x-2">
          <div className="relative group">
            <div className="bg-[#EEE9F2] h-6 w-6 items-center p-1.5 flex justify-center rounded cursor-pointer">  
            <img src={Edit} alt={t('my_events.edit')} onClick={() => onEdit(row)} className="w-4"  />
            </div>
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              {t('my_events.edit')}
            </div>
          </div>
          <div className="relative group">
            <div className="bg-[#EEE9F2] h-6 w-6 items-center p-1.5 flex justify-center rounded cursor-pointer"> 
              <img src={Block} alt={t('my_events.block')} onClick={() => onDelete(row.id)} className="w-4"/>
            </div>
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              {t('my_events.block')}
            </div>
          </div>
          <div className="relative group">
            <div className="bg-[#EEE9F2] h-6 w-6 items-center p-1.5 flex justify-center rounded cursor-pointer"> 
              <img src={View} alt={t('my_events.view')} className="w-4" />
            </div>
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              {t('my_events.view_card')}
            </div>
          </div>
          <div className="relative group">
            <div className="bg-[#EEE9F2] h-6 w-6 items-center p-1.5 flex justify-center rounded cursor-pointer"> 
              <img src={Clone} alt={t('my_events.clone')} className="w-4" />
            </div>
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              {t('my_events.clone_card')}
            </div>
          </div>
        </div>
      ),
    },
  ];

  const filterOptions = [
    { value: "all", label: t('my_events.my_events') },
    { value: "today", label: t('my_events.today_events') },
  ];

  const [visits, setVisits] = useState([]);
  const [activeTab, setActiveTab] = useState("all");
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);
        const response = await getEvents(); // Fetch events using the API
        console.log("API Response:", response); // Debug log

        // Extract events array from nested response structure
        let eventsArray = [];
        if (Array.isArray(response)) {
          eventsArray = response;
        } else if (Array.isArray(response?.data)) {
          eventsArray = response.data;
        } else if (Array.isArray(response?.events)) {
          eventsArray = response.events;
        } else if (Array.isArray(response?.results)) {
          eventsArray = response.results;
        } else if (Array.isArray(response?.data?.data)) {
          eventsArray = response.data.data;
        }

        // Optionally, map enums to readable strings
        const typeMap = { 0: 'Meeting', 1: 'Interview', 2: 'Delivery' }; // Example
        const categoryMap = { 1: 'Board', 2: 'General' }; // Example
        const statusMap = { 0: 'Pending', 1: 'Scheduled', 2: 'Completed' }; // Example

        // Format the events data to match our component expectations
        const formattedEvents = eventsArray.map(event => ({
          ...event,
          id: event.visit_id || event.id || event._id || Date.now() + Math.random(),
          title: event.title || event.EventTitle || event.name || 'Untitled Event',
          type: typeof event.type === 'number' ? (typeMap[event.type] || event.type) : (event.type || event.EventType || event.visit_type || 'General'),
          category: typeof event.category === 'number' ? (categoryMap[event.category] || event.category) : (event.category || 'General'),
          host: event.host?.first_name
            ? `${event.host.first_name} ${event.host.last_name || ''}`.trim()
            : (event.host_name || event.host || ''),
          escort: event.escort?.first_name
            ? `${event.escort.first_name} ${event.escort.last_name || ''}`.trim()
            : (event.escort_name || event.escort || ''),
          status: typeof event.status === 'number' ? (statusMap[event.status] || event.status) : (event.status || 'Scheduled'),
          startDate: event.start_date
            ? moment(event.start_date).format("DD-MMM-YYYY")
            : (event.startDate || (event.startDateTime
              ? moment(event.startDateTime).format("DD-MMM-YYYY | hh:mm A")
              : (event.start_date_time
                ? moment(event.start_date_time).format("DD-MMM-YYYY | hh:mm A")
                : ''))),
          endDate: event.end_date
            ? moment(event.end_date).format("DD-MMM-YYYY")
            : (event.endDate || (event.endDateTime
              ? moment(event.endDateTime).format("DD-MMM-YYYY | hh:mm A")
              : (event.end_date_time
                ? moment(event.end_date_time).format("DD-MMM-YYYY | hh:mm A")
                : ''))),
        }));

        setVisits(formattedEvents); // Update state with formatted events
      } catch (error) {
        console.error("Error fetching events:", error);
        setVisits([]); // fallback to empty array on error
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  // Filter visits based on active tab
  const filteredVisits = useMemo(() => {
    return visits.filter((visit) => {
      if (activeTab === "all") return true;
      if (activeTab === "today") {
        const visitDate = moment(visit.startDate, "DD-MMM-YYYY | hh:mm A");
        return visitDate.isSame(moment(), "day");
      }
      return true;
    });
  }, [visits, activeTab]);

  const handleDelete = (id) => {
    setVisits(visits.filter((visit) => visit.id !== id));
  };

  const handleEdit = (data) => {
    setEditData(data);
    setIsEditModalOpen(true);
  };

  const handleEditSave = (updatedData) => {
    setVisits(
      visits.map((visit) => (visit.id === updatedData.id ? updatedData : visit))
    );
    setIsEditModalOpen(false);
  };

  const handleCreateVisit = () => {
    setIsCreateModalOpen(true);
  };

 const handleCreateSave = async (newVisitData) => {
    try {
      // call API
      const created = await createEvent(newVisitData);
      // format dates if API doesn’t do it for you:
      const formatted = {
        ...created,
        id: created.id || Date.now(), // Ensure we have an ID
        title: created.EventTitle || created.title || newVisitData.EventTitle,
        type: created.EventType || created.type || newVisitData.EventType,
        category: created.category || newVisitData.category || 'General',
        host: created.host || newVisitData.host,
        escort: created.escort || newVisitData.escort || '',
        status: created.status || 'Scheduled',
        startDate: created.startDateTime
          ? moment(created.startDateTime).format("DD-MMM-YYYY | hh:mm A")
          : newVisitData.startDateTime
            ? moment(newVisitData.startDateTime).format("DD-MMM-YYYY | hh:mm A")
            : '',
        endDate: created.endDateTime
          ? moment(created.endDateTime).format("DD-MMM-YYYY | hh:mm A")
          : newVisitData.endDateTime
            ? moment(newVisitData.endDateTime).format("DD-MMM-YYYY | hh:mm A")
            : '',
      };
      setVisits([formatted, ...visits]);
      setIsCreateModalOpen(false);
    } catch (err) {
      console.error("Error creating event:", err);
      // show error to user if desired
    }
  };
  return (
    <div className="pl-24 mt-20 pr-8 h-full">
      {/* Header */}
      <div className="text-[24px] font-normal text-[#4F2683] mb-4">
        <h3>{t('my_events.my_events')}</h3>
      </div>
      <FilterButtons
        filter={activeTab}
        onFilterChange={setActiveTab}
        filterOptions={filterOptions}
      />

      {/* Table Section */}
      <div className="mt-4">
        {loading ? (
          <p>{t('my_events.loading')}</p> // Show loading state
        ) : (
          <GenericTable
            title={t('my_events.my_events')}
            columns={columns(handleEdit, handleDelete)}
            data={filteredVisits}
            onAdd={handleCreateVisit}
          />
        )}
      </div>

      {/* Edit Modal */}
      {isEditModalOpen && (
        <MyVisitsEdit
          data={editData}
          onClose={() => setIsEditModalOpen(false)}
          onSave={handleEditSave}
        />
      )}

      {/* Create Visit Modal */}
      <AddVisitForm
        visible={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSave={handleCreateSave}
      />
    </div>
  );
};
export default MyEvents;
