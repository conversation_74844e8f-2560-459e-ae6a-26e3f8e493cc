import React, { useState } from 'react';
import userImg from "../../Images/fromimg.svg";
import Identity from '../../Components/MyApp/MyTeam/Identity/Identity';
import Corporate from '../../Components/MyApp/MyTeam/Corporate/Corporate';
import Cards from '../../Components/MyApp/MyTeam/Cards/Cards';
import Access from '../../Components/MyApp/MyTeam/Access/Access';
import Delegates from '../../Components/MyApp/MyTeam/Delegates/Delegates';
import Vehicles from '../../Components/MyApp/MyTeam/Vehicles/Vehicles';
import DetailsCard from '../../Components/Global/DetailsCard';
import EditPhotoModal from '../../Components/Global/ImageAndCamera/EditPhotoModal';
import Requests from '../../Components/MyApp/MyTeam/Requests/Requests';
import { useTranslation } from 'react-i18next';

const TeamDetails = () => {
  const { t } = useTranslation();
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isEditingOtherInfo, setIsEditingOtherInfo] = useState(false);
  const [selectedTab, setSelectedTab] = useState('Identity');
  const [profileImage, setProfileImage] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [profileDetails, setProfileDetails] = useState({
    firstName: "Adam",
    middleName: "",
    lastName: "LETHELAN",
    preferredName: "ADAM LETHELAN",
    email: "<EMAIL>",
    workPhone: "123-123456",
    mobilePhone: "1234567890",
  });

  const [otherInfo, setOtherInfo] = useState({
    eid: "1234",
  });

  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileDetails((prev) => ({ ...prev, [name]: value }));
  };

  const handleOtherInfoChange = (e) => {
    const { name, value } = e.target;
    setOtherInfo((prev) => ({ ...prev, [name]: value }));
  };

  const handleImageCaptured = (imageSrc) => {
    setProfileImage(imageSrc);
    setIsModalOpen(false);
  };

  return (
    <div className="bg-gray-100 min-h-screen p-8 pl-24 pt-16">
      {/* Profile Section */}
      <DetailsCard
        OpenPhotoModal={() => setIsModalOpen(true)}
        profileImage={profileImage}
        defaultImage={userImg}
        name="ADAM L'THELAN"
        additionalFields={[
          { label: t('team_details.type'), value: t('team_details.employee') },
          { label: t('team_details.eid'), value: "1222535" },
          { label: t('team_details.department'), value: "ABC" },
          { label: t('team_details.manager'), value: "Nema" },
          { label: t('team_details.status'), value: t('team_details.unprinted_badges') },
        ]}
      />

      <div className="flex gap-8">
        <div className="w-1/12 mt-2">
          {['Identity', 'Corporate', 'Cards', 'Access', 'Delegates', 'Vehicles', 'Requests'].map((tab) => (
            <button
              key={tab}
              className={`block w-full text-left font-normal p-2 mb-2 ${
                selectedTab === tab
                  ? 'text-[#4F2683] text-[18px] border-l-2 border-[#4F2683]'
                  : 'text-[18px] text-gray-700'
              }`}
              onClick={() => setSelectedTab(tab)}
            >
              {t(`team_details.tabs.${tab.toLowerCase()}`)}
            </button>
          ))}
        </div>

        <div className="w-11/12 ml-4">
          {selectedTab === 'Identity' && <Identity />}
          {selectedTab === 'Corporate' && <Corporate />}
          {selectedTab === 'Cards' && <Cards />}
          {selectedTab === 'Access' && <Access />}
          {selectedTab === 'Delegates' && <Delegates />}
          {selectedTab === 'Vehicles' && <Vehicles />}
          {selectedTab === 'Requests' && <Requests />}
        </div>
      </div>

      {isModalOpen && (
        <EditPhotoModal
          onClose={() => setIsModalOpen(false)}
          onSave={handleImageCaptured}
        />
      )}
    </div>
  );
};

export default TeamDetails;
