import React, { useState, useEffect } from 'react';
import DemographicInformation from '../../Components/Observation/DemographicInformation';
import ReasonHandling from '../../Components/Observation/ReasonHandling';
import Document from '../../Components/Observation/Document';
import EditPhotoModal from '../../Components/Global/ImageAndCamera/EditPhotoModal';
import { useNavigate } from 'react-router-dom';
import { IoIosArrowBack } from "react-icons/io";
import HistoryTable from '../../Components/Observation/HistoryTable';
import userImg from "../../Images/fromimg.svg"
import DetailsCard from '../../Components/Global/DetailsCard';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { getWatchlistDetails, getWatchlistById  } from '../../api/watchList';
import { getWatchlistHistoryById } from '../../api/watchList';
import { updateWatchlistImage } from '../../api/watchList';
import { getMediaByModel } from '../../api/global';

const ObservationDatailes = () => {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState(t('observation_details.tabs.demographic_information'));
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [isHistoryPanelOpen, setIsHistoryPanelOpen] = useState(false);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const watchlistId = queryParams.get("id");

  const [watchlistDetails, setWatchlistDetails] = useState(null);
  const navigate = useNavigate();
  const [historyData, setHistoryData] = useState([]);


  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
const [demographicData, setDemographicData] = useState(null);

  const handleHistoryOpen = () => {
    if (watchlistId) {
      setIsLoadingHistory(true);
      getWatchlistHistoryById(watchlistId)
        .then(res => {
          setHistoryData(res?.data?.map(item => ({ ...item, avatar: item.avatar || userImg })));
          setIsHistoryPanelOpen(true);
        })
        .catch(err => {
          console.error("Error fetching history:", err);
        })
        .finally(() => setIsLoadingHistory(false));
    }


  };

  // Function to handle image capture
 const handleImageCaptured = async (imageSrc) => {
     try {
       const response = await updateWatchlistImage(watchlistId, { image: imageSrc });
       const newUrl = response.image || imageSrc;
       setProfileImage(newUrl);
     } catch (err) {
       console.error("Failed to save image:", err);
     } finally {
       setIsModalOpen(false);
     }
   };

  useEffect(() => {
  if (!watchlistId) return;

  // 1️⃣ Fetch the basic details...
  getWatchlistDetails({ watchlist_id: watchlistId })
    .then(async res => {
      const data = res.data;
      setWatchlistDetails(res);

      // 2️⃣ If the API returned an image-ID, fetch its URL and set it:
      const imageId = data.image_id || data.image;
      if (imageId) {
        try {
          const media = await getMediaByModel("Watchlist", {
            key: "image",
            value: imageId,
          });
          setProfileImage(media.value);
        } catch (err) {
          console.error("Error fetching watchlist image:", err);
        }
      }
    })
    .catch(err => {
      console.error("Error fetching watchlist details:", err);
    });

  // 3️⃣ And still fetch your demographic info in parallel:
  getWatchlistById(watchlistId)
    .then(data => setDemographicData(data))
    .catch(err => console.error("Error fetching demographic info:", err));
}, [watchlistId]);

  return (
    <div className="bg-gray-100 min-h-screen p-8 pl-24 pt-16">
      <div className="flex items-center pt-1 text-[#4F2683]">
        <div className='flex items-center gap-1 cursor-pointer' onClick={() => navigate('/observation-roster')}>
          <IoIosArrowBack className="text-[#4F2683] font-normal text-[24px]" />
          <h2 className="font-normal text-[24px]">{t('observation_details.title')}</h2>
        </div>
      </div>
      {watchlistDetails && (
        <DetailsCard
          OpenPhotoModal={() => setIsModalOpen(true)}
          handleHistoryOpen={handleHistoryOpen}
          profileImage={profileImage}
          defaultImage={userImg}
          name={`${watchlistDetails.data.first_name} ${watchlistDetails.data.last_name || ''}`}
          showHistoryButton={true}
          additionalFields={[
            { label: t('observation_details.address'), value: watchlistDetails.data.address || '-' },
            { label: t('observation_details.status'), value: watchlistDetails.data.status === 0 ? t('observation_details.inactive') : t('observation_details.active') },
            { label: t('observation_details.phone_number'), value: watchlistDetails.data.phone || '-' },
          ]}
        />
      )}
      <div className="flex gap-8">
        <div className="w-1/12 mt-2">
          {[t('observation_details.tabs.demographic_information'), t('observation_details.tabs.reason_handling'), t('observation_details.tabs.document')].map((tab) => (
            <button
              key={tab}
              className={`block w-full text-left font-normal p-2 mb-2 ${selectedTab === tab ? 'text-[#4F2683] text-[18px]  border-l-2 border-[#4F2683] font-normal' : 'font-normal text-[18px]  text-gray-700'}`}
              onClick={() => setSelectedTab(tab)}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Main Content */}
        <div className="w-11/12 ml-4">
          {selectedTab === t('observation_details.tabs.demographic_information') && < DemographicInformation data={demographicData?.data} watchlistId={watchlistId} />}
          {selectedTab === t('observation_details.tabs.reason_handling') && <ReasonHandling data={demographicData?.data} watchlistId={watchlistId}  />}
         {selectedTab === t('observation_details.tabs.document') && (
   <Document watchlistId={watchlistId} />
 )}
        </div>
      </div>
      <HistoryTable
        isOpen={isHistoryPanelOpen}
        onClose={() => setIsHistoryPanelOpen(false)}
        data={historyData}
      />

      {isModalOpen && (
        <EditPhotoModal
          // title={modalTitle}
          onClose={() => setIsModalOpen(false)}
          onSave={handleImageCaptured}
        />
      )}
    </div>
  );
};

export default ObservationDatailes;
