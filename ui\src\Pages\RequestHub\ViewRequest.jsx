import React, { useState } from "react";
import GenericTable from "../../Components/GenericTable";
import TruncatedCell from "../../Components/Tooltip/TruncatedCell";
import { RequestApprovalsData, RequestAreaData } from "../../api/static";

const ViewRequest = ({ onClose, requestData }) => {
    const [activeTab, setActiveTab] = useState("Area");
    const [show, setShow] = useState(false);

    React.useEffect(() => {
        const timer = setTimeout(() => setShow(true), 10);
        return () => clearTimeout(timer);
    }, []);

    const areaColumns = [
        { name: "Area Name", selector: (row) => row.area },
        { name: "Facility", selector: (row) => row.facility },
        { name: "Address", selector: (row) => row.address },
        { name: "City", selector: (row) => row.city },
        { name: "State", selector: (row) => row.state },
        { name: "Country", selector: (row) => row.country },
    ];

    const approvalsColumns = [
        {
            name: <TruncatedCell text="Area Name" />,
            selector: (row) => row.area
        },
        {
            name: <TruncatedCell text="Approval Step" />,
            selector: (row) => row.approval
        },
        {
            name: <TruncatedCell text="Task Id" />,
            selector: (row) => row.taskId
        },
        {
            name: <TruncatedCell text="Task Owner" />,
            selector: (row) => row.taskOwner
        },
        {
            name: <TruncatedCell text="Task Set On" />,
            selector: (row) => row.taskSetOn
        },
        {
            name: <TruncatedCell text="Approver" />,
            selector: (row) => row.approver
        },
        {
            name: <TruncatedCell text="Response Date" />,
            selector: (row) => row.responseDate
        },
        {
            name: <TruncatedCell text="Comment" />,
            selector: (row) => row.comment
        },
        {
            name: "Status",
            selector: (row) => row.status,
            cell: (row) => (
                <span
                    className={`px-3 py-1 rounded-full text-sm  ${row.status === "Active"
                        ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
                        : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
                        }`}
                >
                    {row.status}
                </span>
            ),
            center: true,
        },
    ];

    const renderTable = () => {
        switch (activeTab) {
            case "Area":
                return (
                    <GenericTable
                        columns={areaColumns}
                        data={RequestAreaData}
                        showSearch={false}
                        showAddButton={false}
                    />
                );
            case "Approvals":
                return (
                    <GenericTable
                        columns={approvalsColumns}
                        data={RequestApprovalsData}
                        showSearch={false}
                        showAddButton={false}
                    />
                );
            default:
                return null;
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
            <div
                className={`bg-white w-full h-full max-w-5xl p-2 rounded-lg shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"
                    }`}
                style={{ willChange: "transform" }}
            >  <div className="flex items-center justify-between mb-2 px-2">
                    <h2 className="text-[24px] md:text-[30px] font-normal text-[#4F2683]">
                        View Request Details
                    </h2>
                    <button
                        className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
                        type="button"
                        onClick={() => {
                            setShow(false);
                            setTimeout(onClose, 700);
                        }}
                    >
                        &times;
                    </button>
                </div>
                <hr className="mx-3 mb-4" />
                <div className="px-4 mb-6 text-[14px] md:text-[16px] text-[#333333] space-y-2">
                    <h2 className="text-[20px] font-medium mb-2" >Request Details</h2>
                    <div className="flex flex-col  w-1/2">
                        <div className="flex justify-between">
                            <label className="text-[16px] w-full  font-normal">Request ID:</label>
                            <div className="w-full text-[16px] font-normal text-[#8F8F8F]">
                                {requestData.requestId}
                            </div>
                        </div>
                        <div className="flex justify-between">
                            <label className="text-[16px] w-full font-normal">Requested Date:</label>
                            <div className="w-full text-[16px] font-normal text-[#8F8F8F]">
                                {requestData.requestedDate}
                            </div>
                        </div>
                        <div className="flex justify-between">
                            <label className="text-[16px] w-full font-normal">Requested For:</label>
                            <div className="w-full text-[16px] font-normal text-[#8F8F8F]">
                                {requestData.requestFor}
                            </div>
                        </div>
                        <div className="flex justify-between">
                            <label className="text-[16px] w-full font-normal">Approver:</label>
                            <div className="w-full text-[16px] font-normal text-[#8F8F8F]">
                                {requestData.approver}
                            </div>
                        </div>
                        <div className="flex justify-between">
                            <label className="text-[16px] w-full font-normal">Start Date:</label>
                            <div className="w-full text-[16px] font-normal text-[#8F8F8F]">
                                {requestData.startDate}
                            </div>
                        </div>
                        <div className="flex justify-between">
                            <label className="text-[16px] w-full font-normal">End Date:</label>
                            <div className="w-full text-[16px] font-normal text-[#8F8F8F]">
                                {requestData.endDate}
                            </div>
                        </div>
                        <div className="flex justify-between">
                            <label className="text-[16px] w-full font-normal">Requested By:</label>
                            <div className="w-full text-[16px] font-normal text-[#8F8F8F]">
                                {requestData.requestedBy}
                            </div>
                        </div>
                        <div className="flex justify-between">
                            <label className="text-[16px] w-full font-normal">Justification:</label>
                            <div className="w-full text-[16px] font-normal text-[#8F8F8F]">
                                {requestData.justification}
                            </div>
                        </div>
                    </div>

                </div>

                <div className="px-4 mb-4">
                    <div className="flex gap-2">
                        <button
                            onClick={() => setActiveTab("Area")}
                            className={`px-4 py-2 rounded-full ${activeTab === "Area"
                                ? "bg-[#4F2683] text-white"
                                : "bg-gray-200 text-[#333]"
                                }`}
                        >
                            Area
                        </button>
                        <button
                            onClick={() => setActiveTab("Approvals")}
                            className={`px-4 py-2 rounded-full ${activeTab === "Approvals"
                                ? "bg-[#4F2683] text-white"
                                : "bg-gray-200 text-[#333]"
                                }`}
                        >
                            Approvals
                        </button>
                    </div>
                </div>

                <div className="px-4">{renderTable()}</div>
            </div>
        </div>
    );
};

export default ViewRequest;
