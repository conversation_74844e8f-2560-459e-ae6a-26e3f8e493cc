import React, { useState, useMemo } from "react";
import moment from "moment";
import GenericTable from "../../Components/GenericTable";
import { FilterButtons } from "../../Components/GenericTable";
import TemporaryCardAdd from "../../Components/Temporary/TemporaryCardAdd";
import TruncatedCell from "../../Components/Tooltip/TruncatedCell";
import TruncatedRow from "../../Components/Tooltip/TrucantedRow";
import Edit from "../../Images/editPen.svg"
import Return from "../../Images/returnIcon.svg"
import Swal from "sweetalert2";

const initialTempCards = [
    {
        id: 1,
        name: "ADAM L'THELAN",
        EID: "300074091445",
        type: "Employee",
        hiringCompany: "Oracle Corp.",
        facility: "SYDNEY, North B",
        manager: "DAMIEN PIGOTT",
        tempCardNo: "300074091445",
        activationDateTime: "1-Apr-2025 12:00 AM",
        deactivationDateTime: "25-Apr-2025 12:00 AM",
        status: "Not Returned",
        justification: "Lost Permanent Card",
    },
    {
        id: 2,
        name: "ADAM MYERS",
        EID: "300074091446",
        type: "Employee",
        hiringCompany: "Oracle America",
        facility: "SYDNEY, North B",
        manager: "DAMIEN PIGOTT",
        tempCardNo: "300074091446",
        activationDateTime: "1-Apr-2025 09:00 AM",
        deactivationDateTime: "15-Apr-2025 09:00 AM",
        status: "Returned",
        justification: "Forgot At Home",
    },
    {
        id: 3,
        name: "AGNESZKA BURKE",
        EID: "300074091447",
        type: "Contractor",
        hiringCompany: "Oracle Corp.",
        facility: "SYDNEY, North A",
        manager: "DAMIEN PIGOTT",
        tempCardNo: "300074091447",
        activationDateTime: "15-Mar-2025 11:00 AM",
        deactivationDateTime: "16-Mar-2025 11:00 AM",
        status: "Not Returned",
        justification: "Forgot Permanent Card",
    },
];

const TemporaryCards = () => {
    const [tempCards, setTempCards] = useState(initialTempCards);
    const [activeFilter, setActiveFilter] = useState("all");
    const [tableSearchTerm, setTableSearchTerm] = useState("");
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [editCard, setEditCard] = useState(null);

    const getEditInitialValues = (card) => ({
        searchIdentity: {
            name: card.name || "",
            eid: card.EID || "",
            company: card.hiringCompany || "",
            manager: card.manager || "",
        },
        activationDateTime: new Date(card.activationDateTime),
        deactivationDateTime: new Date(card.deactivationDateTime),
        cardType: card.type || "",
        cardNumber: card.tempCardNo || "",
        facilityBuilding: card.facility || "",
        justification: card.justification || "",
    });

    const columns = [
        {
            name: "Name",
            selector: (row) => row.name,
            cell: (row) => <TruncatedRow text={row.name} />,
            sortable: true,
        },
        {
            name: "EID",
            selector: (row) => row.EID,
            cell: (row) => <TruncatedRow text={row.EID} />,
        },
        {
            name: "Type",
            selector: (row) => row.type,
            cell: (row) => <TruncatedRow text={row.type} />,
        },
        {
            name: <TruncatedCell text="Hiring Company" />,
            selector: (row) => row.hiringCompany,
            cell: (row) => <TruncatedRow text={row.hiringCompany} />,
        },
        {
            name: <TruncatedCell text="Facility" />,
            selector: (row) => row.facility,
            cell: (row) => <TruncatedRow text={row.facility} />,
        },
        {
            name: <TruncatedCell text="Manager" />,
            selector: (row) => row.manager,
            cell: (row) => <TruncatedRow text={row.manager} />,
        },
        {
            name: <TruncatedCell text="Temp Card Number" />,
            selector: (row) => row.tempCardNo,
            cell: (row) => <TruncatedRow text={row.tempCardNo} />,
        },
        {
            name: <TruncatedCell text="Activation Date/Time" />,
            selector: (row) => row.activationDateTime,
            cell: (row) => <TruncatedRow text={row.activationDateTime} />,
        },
        {
            name: <TruncatedCell text="Deactivation Date/Time" />,
            selector: (row) => row.deactivationDateTime,
            cell: (row) => <TruncatedRow text={row.deactivationDateTime} />,
        },
        {
            name: "Status",
            selector: (row) => row.status,
            cell: (row) => (
                <span
                    className={`w-28 py-1 flex justify-center items-center rounded-full ${row.status.toLowerCase() === "returned"
                            ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
                            : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
                        }`}
                >
                    {row.status}
                </span>
            ),
        },
        {
            name: "Actions",
            cell: (row) => (
                <div className="flex space-x-1">
                    <img
                        src={Edit}
                        alt="Edit"
                        onClick={() => handleEdit(row)}
                        className="bg-[#EEE9F2] px-1 py-1 rounded cursor-pointer"
                    />
                    <img
                        src={Return}
                        alt="Return"
                        onClick={() => row.status.toLowerCase() !== "returned" && handleChangeStatus(row)}
                        className={`bg-[#EEE9F2] px-1 py-1 rounded ${row.status.toLowerCase() === "returned" ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
                            }`}
                    />
                </div>
            ),
        },

    ];

    const filteredData = useMemo(() => {
        const now = moment();
        return tempCards.filter((card) => {
            if (activeFilter === "all") return true;
            if (activeFilter === "notReturned") {
                return card.status.toLowerCase() === "not returned";
            }
            if (activeFilter === "last7") {
                const activation = moment(card.activationDateTime, "MMM-DD-YYYY hh:mm A");
                return now.diff(activation, "days") <= 7;
            }
            if (activeFilter === "last30") {
                const activation = moment(card.activationDateTime, "MMM-DD-YYYY hh:mm A");
                return now.diff(activation, "days") <= 30;
            }
            if (activeFilter === "last60") {
                const activation = moment(card.activationDateTime, "MMM-DD-YYYY hh:mm A");
                return now.diff(activation, "days") <= 60;
            }
            return true;
        });
    }, [tempCards, activeFilter]);

    const filterOptions = [
        { value: "all", label: "All Issued Temp Cards" },
        { value: "notReturned", label: "Not Returned" },
        { value: "last7", label: "Last 7 Days" },
        { value: "last30", label: "Last 30 Days" },
        { value: "last60", label: "Last 60 Days" },
    ];

    const handleAddTemporaryCard = (newCard) => {
        newCard.id = tempCards.length + 1;
        setTempCards((prevCards) => [newCard, ...prevCards]); 
    };
    
    const handleEdit = (card) => {
        setEditCard(card);
        setIsEditModalOpen(true);
    };

    const handleChangeStatus = (card) => {
        Swal.fire({
            title: 'Are you sure?',
            text: "Do you want to mark this card as returned?",
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#4F2683',
            cancelButtonColor: '#8F8F8F',
            confirmButtonText: 'Yes',
            cancelButtonText: 'No'
        }).then((result) => {
            if (result.isConfirmed) {
                setTempCards((prevCards) =>
                    prevCards.map((c) =>
                        c.id === card.id ? { ...c, status: "Returned" } : c
                    )
                );
            }
        });
    };
    
    return (
        <div className="pl-24 mt-20 pr-8 h-full">
            <div className="text-[24px] font-normal text-[#4F2683] mb-4">
                <h3>Temporary Cards</h3>
            </div>

            <FilterButtons
                filter={activeFilter}
                onFilterChange={setActiveFilter}
                filterOptions={filterOptions}
            />

            <div className="mt-4">
                <GenericTable
                    title={"Temporary Cards"}
                    searchTerm={tableSearchTerm}
                    onSearchChange={(e) => setTableSearchTerm(e.target.value)}
                    columns={columns}
                    data={filteredData}
                    onAdd={() => setIsAddModalOpen(true)}
                    fixedHeader
                    fixedHeaderScrollHeight="400px"
                    highlightOnHover
                    striped
                />
            </div>

            {isAddModalOpen && (
                <TemporaryCardAdd
                    onClose={() => setIsAddModalOpen(false)}
                    onAdd={(card) => {
                        handleAddTemporaryCard(card);
                        setIsAddModalOpen(false);
                    }}
                />
            )}

            {isEditModalOpen && editCard && (
                <TemporaryCardAdd
                    initialValues={getEditInitialValues(editCard)}
                    onClose={() => {
                        setIsEditModalOpen(false);
                        setEditCard(null);
                    }}
                    onAdd={(updatedCard) => {
                        updatedCard.id = editCard.id;
                        setTempCards((prevCards) =>
                            prevCards.map((c) => (c.id === updatedCard.id ? updatedCard : c))
                        );
                        setIsEditModalOpen(false);
                        setEditCard(null);
                    }}
                />
            )}
        </div>
    );
};

export default TemporaryCards;
