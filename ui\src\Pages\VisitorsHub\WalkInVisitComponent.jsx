import React, { useState } from "react";
import WalkInVisitForm from "../../Components/Global/Forms/WalkInVisitForm";

const WalkInVisitComponent = ({ fieldsToRender, onAddGuest }) => {
  const [showWalkInVisitForm, setShowWalkInVisitForm] = useState(false);

  const handleToggleWalkInVisitForm = () => {
    setShowWalkInVisitForm((prev) => !prev);
  };

  return (
    <div>
      <button
        className="bg-[#4F2683] text-white px-4 py-2 rounded-md"
        onClick={handleToggleWalkInVisitForm}
      >
        {showWalkInVisitForm ? "Close Walk-In Visit Form" : "Open Walk-In Visit Form"}
      </button>

      {showWalkInVisitForm && (
        <WalkInVisitForm
          fieldsToRender={fieldsToRender}
          onAddGuest={onAddGuest}
          onClose={handleToggleWalkInVisitForm}
        />
      )}
    </div>
  );
};

export default WalkInVisitComponent;
