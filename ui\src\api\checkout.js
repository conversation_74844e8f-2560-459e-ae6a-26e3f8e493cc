/**
 * Check out a guest for a specific visit.
 *
 * @param {string} visitId - The ID of the visit.
 * @param {string} guestId - The ID of the guest.
 * @returns {Promise<any>} A promise that resolves to the check-out response.
 */
export const checkoutGuestForVisit = async (visitId, guestId) => {
  const response = await api.post(`/visits/${visitId}/guest/${guestId}/checkout`);
  return response.data;
};