import patientImg from "../Images/patient.png";
// import guestImage from "../Images/dddemo.svg";
import guestImage from "../Images/guest-image1.png";
import defaultImg from "../Images/demoimg.svg"

export  const defaultIdentities = [
  {
    image:defaultImg,
    name: "<PERSON>",
    eid: "E001",
    type: "Employee",
    company: "Company A",
    organization: "Org1",
    jobTitle: "Developer",
    endDate: "2023-12-31",
    manager:"Darshi<PERSON>",
    status: "Active",
  },
  {
    image:defaultImg,
    name: "<PERSON>",
    eid: "E002",
    type: "Manager",
    company: "Company B",
    organization: "Org2",
    jobTitle: "Team Lead",
    endDate: "2023-11-30",
    manager:"Nema",
    status: "Active",
  },
  {
    image:defaultImg,
    name: "<PERSON>",
    eid: "E003",
    type: "Employee",
    company: "Company C",
    organization: "Org3",
    jobTitle: "Designer",
    endDate: "2023-10-15",
    manager:"Amit",
    status: "Inactive",
  },
  {
    image:defaultImg,
    name: "<PERSON>",
    eid: "E004",
    type: "Director",
    company: "Company D",
    organization: "Org4",
    jobTitle: "Project Manager",
    endDate: "2023-09-30",
    manager:"Nav",
    status: "Active",
  },
  {
    image:defaultImg,
    name: "Edward Norton",
    eid: "E005",
    type: "Employee",
    company: "Company E",
    organization: "Org5",
    jobTitle: "QA Engineer",
    endDate: "2023-08-31",
    manager:"Mav",
    status: "Active",
  },
  {
    image:defaultImg,
    name: "Fiona Gallagher",
    eid: "E006",
    type: "Manager",
    company: "Company F",
    organization: "Org6",
    jobTitle: "HR Manager",
    endDate: "2023-07-31",
    manager:"Jay",
    status: "Inactive",
  },
  {
    image:defaultImg,
    name: "George Clooney",
    eid: "E007",
    type: "Employee",
    company: "Company G",
    organization: "Org7",
    jobTitle: "Support",
    endDate: "2023-06-30",
    manager:"Ajay",
    status: "Active",
  },
  {
    image:defaultImg,
    name: "Hannah Montana",
    eid: "E008",
    type: "Employee",
    company: "Company H",
    organization: "Org8",
    jobTitle: "Sales",
    endDate: "2023-05-31",
    manager:"John",
    status: "Active",
  },
];

export const SearchableVisitors =[
  {
    id:1,
    image:guestImage,
    firstName:"Mohan",
    lastName:"Goyal",
    email:"example.com",
    mobile:"0987654321",
    Company:"ABCD",
    privateVisitor:"You"
  },
  {
    id:2,
    image:guestImage,
    firstName:"Aman",
    lastName:"Goyal",
    email:"example.com",
    mobile:"0987654321",
    Company:"ABCD",
    privateVisitor:"You"
  },
  {
    id:3,
    image:guestImage,
    firstName:"Nav",
    lastName:"Mav",
    email:"example.com",
    mobile:"0987654321",
    Company:"ABCD",
    privateVisitor:"You"
  },
  {
    id:4,
    image:guestImage,
    firstName:"Noise",
    lastName:"Maker",
    email:"example.com",
    mobile:"0987654321",
    Company:"ABCD",
    privateVisitor:"You"
  },
]

export const secondTableData = [
  {
    guestName: "John Doe",
    relationship: "Friend",
    restraining: "No",
    email: "<EMAIL>",
    mobile: "************",
  },
  {
    guestName: "Jane Smith",
    relationship: "Sister",
    restraining: "No",
    email: "<EMAIL>",
    mobile: "************",
  },
  {
    guestName: "Bob Brown",
    relationship: "Colleague",
    restraining: "No",
    email: "<EMAIL>",
    mobile: "************",
  },
  {
    guestName: "John Doe",
    relationship: "Friend",
    restraining: "No",
    email: "<EMAIL>",
    mobile: "************",
  },
  {
    guestName: "Jane Smith",
    relationship: "Sister",
    restraining: "No",
    email: "<EMAIL>",
    mobile: "************",
  },
  {
    guestName: "Bob Brown",
    relationship: "Colleague",
    restraining: "No",
    email: "<EMAIL>",
    mobile: "************",
  },
];
export const PatientCardDetails = [
  {
    id: "AM83191",
    name: "Edith Stewart",
    image: patientImg,
    site: "Critical Care",
    room: "332",
    bed: "2",
    confidential: "Yes",
    physician: "Dr. Joe Jonas",
    maxVisitors: "1 of 3",
    guestList: [
      {
        patientId: "AM83191",
        id: "guest-1",
        image: guestImage,
        guestName: "Doe ",
        screening: false,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83191",
        id: "guest-2",
        image: guestImage,
        guestName: "Jane",
        screening: false,
        arrivalTime: "11:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83191",
        id: "guest-3",
        image: guestImage,
        guestName: "Doe Jane",
        screening: true,
        arrivalTime: "09:00 AM",
        mrn: "1234",
      },
    ],
  },
  {
    id: "AM83192",
    name: "Jonas Anthony",
    image: patientImg,
    site: "Emergency",
    room: "335",
    bed: "7",
    confidential: "No",
    physician: "Dr. Jaiveer Bana",
    maxVisitors: "3 of 4",
    guestList: [
      {
        patientId: "AM83192",
        id: "guest-4",
        image: guestImage,
        guestName: "Marshal",
        screening: false,
        arrivalTime: "12:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83192",
        id: "guest-5",
        image: guestImage,
        guestName: "Mellow",
        screening: true,
        arrivalTime: "09:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83192",
        id: "guest-5",
        image: guestImage,
        guestName: "Marshal Mellow",
        screening: true,
        arrivalTime: "08:30 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83192",
        id: "guest-6",
        image: guestImage,
        guestName: "Yellow",
        screening: true,
        arrivalTime: "12:30 AM",
        mrn: "1234",
      },
    ],
  },
  {
    id: "AM83193",
    name: "Jonas Dell",
    image: guestImage,
    site: "Critical",
    room: "336",
    bed: "4",
    Confidential: "yes",
    physician: "Dr. Chrome",
    maxVisitors: "1 of 3",
    guestList: [
      {
        patientId: "AM83193",
        id: "guest-7",
        image: guestImage,
        guestName: "Dell",
        screening: false,
        arrivalTime: "2025-01-29,10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83193",
        id: "guest-8",
        image: guestImage,
        guestName: " Doe",
        screening: true,
        arrivalTime: "2025-01-19,10:40 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83193",
        id: "guest-9",
        image: guestImage,
        guestName: "Dell Doe",
        screening: false,
        arrivalTime: "2025-01-12,11:45 AM",
        mrn: "1234",
      },
    ],
  },
  {
    id: "AM83194",
    name: "Aakash",
    image: patientImg,
    site: "Critical",
    room: "346",
    bed: "5",
    Confidential: "yes",
    physician: "Dr. JavaScript",
    maxVisitors: "2 of 3",
    guestList: [
      {
        patientId: "AM83194",
        id: "guest-10",
        image: guestImage,
        guestName: "Jane Doe",
        screening: true,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83194",
        id: "guest-10",
        image: guestImage,
        guestName: "Jane Doe",
        screening: true,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83194",
        id: "guest-10",
        image: guestImage,
        guestName: "Jane Doe",
        screening: true,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83194",
        id: "guest-10",
        image: guestImage,
        guestName: "Jane Doe",
        screening: true,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83194",
        id: "guest-10",
        image: guestImage,
        guestName: "Jane Doe",
        screening: true,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83194",
        id: "guest-11",
        image: guestImage,
        guestName: "Jane Doe",
        screening: true,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83194",
        id: "guest-12",
        image: guestImage,
        guestName: "Jane Doe",
        screening: false,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
    ],
  },
];
export const OutPatientListDetails2 = [
  {
    id: "400151",
    name: "Edith Stewart",
    image: patientImg,
    room: "13",
    bed: "22",
    appointmentTime: "02/05/2025,10:00 AM",
    site: "",
    suite: "Critical Care",
    confidential: "",
    physician: "Dr. Joe Jonas",
    maxVisitors: "0 of 3",
    dob: "Dec-12-1996",
    guestList: [{}],
    appointment: [
      {
        name: "Edith Stewart",
        image: patientImg,
        doctor: "Dr. Bob",
        appointmentTime: "02/13/2025,01:00 PM",
      },
      {
        name: "Edith Stewart",
        image: patientImg,
        doctor: "Dr. Jay",
        appointmentTime: "03/15/2025,11:00 AM",
      },
      {
        name: "Edith Stewart",
        image: patientImg,
        doctor: "Dr. Dell",
        appointmentTime: "04/15/2025,11:00 AM",
      },
      {
        name: "Edith Stewart",
        image: patientImg,
        doctor: "Dr. Noise",
        appointmentTime: "05/15/2025,11:00 AM",
      },
      {
        name: "Edith Stewart",
        image: patientImg,
        doctor: "Dr. Super",
        appointmentTime: "06/15/2025,11:00 AM",
      },
    ],
  },
  {
    id: "400152",
    name: "Aakash",
    image: patientImg,
    room: "23",
    bed: "22",
    appointmentTime: "02/04/2025,10:00 AM",
    site: "",
    suite: "Critical Care",
    confidential: "",
    physician: "Dr. Joe Jonas",
    maxVisitors: "0 of 3",
    dob: "Dec-12-1996",
    appointment: [
      {
        name: "Aakash",
        image: patientImg,
        doctor: "Dr. Bob",
        appointmentTime: "02/05/2025,01:00 PM",
      },
      {
        name: "Aakash",
        image: patientImg,
        doctor: "Dr. Jay",
        appointmentTime: "03/15/2025,11:00 AM",
      },
      {
        name: "Aakash",
        image: patientImg,
        doctor: "Dr. Dell",
        appointmentTime: "04/15/2025,11:00 AM",
      },
      {
        name: "Aakash",
        image: patientImg,
        doctor: "Dr. Noise",
        appointmentTime: "05/15/2025,11:00 AM",
      },
      {
        name: "Aakash",
        image: patientImg,
        doctor: "Dr. Super",
        appointmentTime: "06/15/2025,11:00 AM",
      },
    ],
  },
  {
    id: "400153",
    name: "Nema",
    image: patientImg,
    room: "33",
    bed: "22",
    appointmentTime: "02/04/2025,10:00 AM",
    site: "",
    suite: "Critical Care",
    confidential: "",
    physician: "Dr. Joe Jonas",
    maxVisitors: "0 of 3",
    dob: "Dec-12-1996",
    appointment: [
      {
        name: "Nema",
        image: patientImg,
        doctor: "Dr. Bob",
        appointmentTime: "02/04/2025,01:00 PM",
      },
      {
        name: "Nema",
        image: patientImg,
        doctor: "Dr. Jay",
        appointmentTime: "03/15/2025,11:00 AM",
      },
      {
        name: "Nema",
        image: patientImg,
        doctor: "Dr. Dell",
        appointmentTime: "04/15/2025,11:00 AM",
      },
      {
        name: "Nema",
        image: patientImg,
        doctor: "Dr. Noise",
        appointmentTime: "05/15/2025,11:00 AM",
      },
      {
        name: "Nema",
        image: patientImg,
        doctor: "Dr. Super",
        appointmentTime: "06/15/2025,11:00 AM",
      },
    ],
  },
  {
    id: "400154",
    name: "Darshil",
    image: patientImg,
    room: "45",
    bed: "23",
    appointmentTime: "02/04/2025,10:00 AM",
    site: "",
    suite: "Critical Care",
    confidential: "",
    physician: "Dr. Joe Jonas",
    maxVisitors: "0 of 3",
    dob: "Dec-12-1996",
    appointment: [
      {
        name: "Darshil",
        image: patientImg,
        doctor: "Dr. Bob",
        appointmentTime: "02/04/2025,01:00 PM",
      },
      {
        name: "Darshil",
        image: patientImg,
        doctor: "Dr. Jay",
        appointmentTime: "03/15/2025,11:00 AM",
      },
      {
        name: "Darshil",
        image: patientImg,
        doctor: "Dr. Dell",
        appointmentTime: "04/15/2025,11:00 AM",
      },
      {
        name: "Darshil",
        image: patientImg,
        doctor: "Dr. Noise",
        appointmentTime: "05/15/2025,11:00 AM",
      },
      {
        name: "Darshil",
        image: patientImg,
        doctor: "Dr. Super",
        appointmentTime: "06/15/2025,11:00 AM",
      },
    ],
  },
  {
    id: "400155",
    name: "Monu",
    image: patientImg,
    room: "53",
    bed: "22",
    appointmentTime: "02/04/2025,10:00 AM",
    site: "",
    suite: "Critical Care",
    confidential: "",
    physician: "Dr. Joe Jonas",
    maxVisitors: "0 of 3",
    dob: "Dec-12-1996",
    appointment: [
      {
        name: "Monu",
        image: patientImg,
        doctor: "Dr. Bob",
        appointmentTime: "02/04/2025,01:00 PM",
      },
      {
        name: "Monu",
        image: patientImg,
        doctor: "Dr. Jay",
        appointmentTime: "03/15/2025,11:00 AM",
      },
      {
        name: "Monu",
        image: patientImg,
        doctor: "Dr. Dell",
        appointmentTime: "04/15/2025,11:00 AM",
      },
      {
        name: "Monu",
        image: patientImg,
        doctor: "Dr. Noise",
        appointmentTime: "05/15/2025,11:00 AM",
      },
      {
        name: "Monu",
        image: patientImg,
        doctor: "Dr. Super",
        appointmentTime: "06/15/2025,11:00 AM",
      },
    ],
  },
  {
    id: "400156",
    name: "Akash",
    image: patientImg,
    room: "311",
    bed: "44",
    appointmentTime: "02/04/2025,10:00 AM",
    site: "",
    suite: "Critical Care",
    confidential: "",
    physician: "Dr. Joe Jonas",
    maxVisitors: "0 of 3",
    dob: "Dec-12-1996",
    appointment: [
      {
        name: "Akash",
        image: patientImg,
        doctor: "Dr. Bob",
        appointmentTime: "02/04/2025,01:00 PM",
      },
      {
        name: "Akash",
        image: patientImg,
        doctor: "Dr. Jay",
        appointmentTime: "03/15/2025,11:00 AM",
      },
      {
        name: "Akash",
        image: patientImg,
        doctor: "Dr. Dell",
        appointmentTime: "04/15/2025,11:00 AM",
      },
      {
        name: "Akash",
        image: patientImg,
        doctor: "Dr. Noise",
        appointmentTime: "05/15/2025,11:00 AM",
      },
      {
        name: "Akash",
        image: patientImg,
        doctor: "Dr. Super",
        appointmentTime: "06/15/2025,11:00 AM",
      },
    ],
  },
];
export const VisitorsHubData = [
  {
    id: "AM83191",
    name: "Edith Stewart",
    image: patientImg,
    site: "Critical Care",
    room: "332",
    bed: "2",
    confidential: "Yes",
    physician: "Dr. Joe Jonas",
    maxVisitors: "1 of 3",
    guestList: [
      {
        patientId: "AM83191",
        id: "guest-1",
        image: guestImage,
        guestName: "Doe ",
        screening: false,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83191",
        id: "guest-2",
        image: guestImage,
        guestName: "Jane",
        screening: false,
        arrivalTime: "11:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83191",
        id: "guest-3",
        image: guestImage,
        guestName: "Doe Jane",
        screening: true,
        arrivalTime: "09:00 AM",
        mrn: "1234",
      },
    ],
  },
  {
    id: "AM83192",
    name: "Jonas Anthony",
    image: patientImg,
    site: "Emergency",
    room: "335",
    bed: "7",
    confidential: "No",
    physician: "Dr. Jaiveer Bana",
    maxVisitors: "3 of 4",
    guestList: [
      {
        patientId: "AM83192",
        id: "guest-4",
        image: guestImage,
        guestName: "Marshal",
        screening: false,
        arrivalTime: "12:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83192",
        id: "guest-5",
        image: guestImage,
        guestName: "Mellow",
        screening: true,
        arrivalTime: "09:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83192",
        id: "guest-5",
        image: guestImage,
        guestName: "Marshal Mellow",
        screening: true,
        arrivalTime: "08:30 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83192",
        id: "guest-6",
        image: guestImage,
        guestName: "Yellow",
        screening: true,
        arrivalTime: "12:30 AM",
        mrn: "1234",
      },
    ],
  },
  {
    id: "AM83193",
    name: "Jonas Dell",
    image: guestImage,
    site: "Critical",
    room: "336",
    bed: "4",
    Confidential: "yes",
    physician: "Dr. Chrome",
    maxVisitors: "1 of 3",
    guestList: [
      {
        patientId: "AM83193",
        id: "guest-7",
        image: guestImage,
        guestName: "Dell",
        screening: false,
        arrivalTime: "2025-01-29,10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83193",
        id: "guest-8",
        image: guestImage,
        guestName: " Doe",
        screening: true,
        arrivalTime: "2025-01-19,10:40 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83193",
        id: "guest-9",
        image: guestImage,
        guestName: "Dell Doe",
        screening: false,
        arrivalTime: "2025-01-12,11:45 AM",
        mrn: "1234",
      },
    ],
  },
  {
    id: "AM83194",
    name: "Aakash",
    image: patientImg,
    site: "Critical",
    room: "346",
    bed: "5",
    Confidential: "yes",
    physician: "Dr. JavaScript",
    maxVisitors: "2 of 3",
    guestList: [
      {
        patientId: "AM83194",
        id: "guest-10",
        image: guestImage,
        guestName: "Jane Doe",
        screening: true,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83194",
        id: "guest-10",
        image: guestImage,
        guestName: "Jane Doe",
        screening: true,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83194",
        id: "guest-10",
        image: guestImage,
        guestName: "Jane Doe",
        screening: true,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83194",
        id: "guest-10",
        image: guestImage,
        guestName: "Jane Doe",
        screening: true,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83194",
        id: "guest-10",
        image: guestImage,
        guestName: "Jane Doe",
        screening: true,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83194",
        id: "guest-11",
        image: guestImage,
        guestName: "Jane Doe",
        screening: true,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
      {
        patientId: "AM83194",
        id: "guest-12",
        image: guestImage,
        guestName: "Jane Doe",
        screening: false,
        arrivalTime: "10:00 AM",
        mrn: "1234",
      },
    ],
  },
];
export const VisitorsData = [
  {
    visitorId:1,
    visitorName:"Vicky",
    image: patientImg,
    visitorHost:"Terry",
    facility:"Atrium",
    startDate:"4-04-2022 10:00 AM",
    endDate:"5-04-2022 8:00 PM",
    visitorDob:"12-05-1996",
    visitorMail:"<EMAIL>",
    visitorPhone:"+**********",
  },
  {
    visitorId:2,
    visitorName:"Nav",
    image: patientImg,
    visitorHost:"Jarry",
    facility:"Atrium",
    startDate:"2-04-2022 10:00 AM",
    endDate:"3-04-2022 8:00 PM",
    visitorDob:"12-05-1996",
    visitorMail:"<EMAIL>",
    visitorPhone:"+**********",
  },
  {
    visitorId:3,
    visitorName:"Mav",
    image: patientImg,
    visitorHost:"Terry",
    facility:"Atrium",
    startDate:"12-04-2022 10:00 AM",
    endDate:"13-04-2022 8:00 PM",
    visitorDob:"12-05-1996",
    visitorMail:"<EMAIL>",
    visitorPhone:"+**********",
  },
]
export const VisitorsDoctorsData = [
  {
    id: 1,
    name: "Randall Moran",
    image:defaultImg,
    site: "Atrium Mercy",
    jobTittle:"Senior Researcher",
    eid:"CA445566",
    status:"Active",
    startDate:"Jan-05-2025",
    endDate:"-",
    visitorList: [
      {
        id: 101,
        visitorName: "Terry Fontaine",
        image:patientImg,
        hostId: 1,
        startDate: "3-25-2025 10:00 AM",
        endDate: "3-26-2025 10:00 AM",
        invited: true,
        checkedIn: false,
        checkedOut: true,
        checkInDenied:true,
      },
      {
        id: 102,
        visitorName: "Fred Smith",
        image:patientImg,
        hostId: 1,
        startDate: "3-26-2025 10:00 AM",
        endDate: "3-27-2025 10:00 AM",
        invited: false,
        checkedIn: true,
        checkedOut: false,
        checkInDenied:false,
      },
    ],
  },
  {
    id: 2,
    name: "Chris Revill",
    image:defaultImg,
    site: "Cleveland Hospital",
    jobTittle:"Senior Researcher",
    eid:"CA445568",
    status:"Active",
    startDate:"Jan-05-2025",
    endDate:"-",
    visitorList: [
      {
        id: 201,
        visitorName: "Jamie Garcia",
        image:patientImg,
        hostId: 2,
        startDate: "3-24-2025 10:00 AM",
        endDate: "3-25-2025 10:00 AM",
        invited: true,
        checkedIn: false,
        checkedOut: true,
        checkInDenied:true,
      },
      {
        id: 202,
        visitorName: "Adam Johnson",
        image:patientImg,
        hostId: 2,
        startDate: "3-24-2025 10:00 AM",
        endDate: "3-25-2025 10:00 AM",
        invited: true,
        checkedIn: false,
        checkedOut: false,
        checkInDenied:true,
      },
    ],
  },
  {
    id: 3,
    name: "Dr .Michael Brown",
    image:defaultImg,
    site: "Fifth Avenue Clinic",
    jobTittle:"Doctor",
    eid:"CA445567",
    status:"Active",
    startDate:"Jan-05-2025",
    endDate:"-",
    visitorList: [
      {
        id: 301,
        visitorName: "Jane Williams",
        image:patientImg,
        hostId: 3,
        startDate: "3-26-2025 10:00 AM",
        endDate: "3-27-2025 10:00 AM",
        invited: false,
        checkedIn: true,
        checkedOut: true,
        checkInDenied:true,
      },
    ],
  },
];

export const Observationdetails = [
  {
    id: 1,
    name: "Observation 1",
    addedBy: "John Doe",
    addedOn: "2023-12-20",
    expirationDate: "2024-12-20",
    status: "Active",
    image: guestImage, // using existing imported image
  },
  {
    id: 2,
    name: "Observation 2",
    addedBy: "Jane Smith",
    addedOn: "2023-11-15",
    expirationDate: "2024-11-15",
    status: "Active",
    image: guestImage, // using existing imported image
  },
  {
    id: 4,
    name: "Observation 24",
    addedBy: "Jane Smith",
    addedOn: "2023-11-15",
    expirationDate: "2024-11-15",
    status: "Inactive",
    image: guestImage, // using existing imported image
  },
  {
    id: 3,
    name: "Observation 21",
    addedBy: "Jane Smith",
    addedOn: "2023-11-15",
    expirationDate: "2024-11-15",
    status: "Inactive",
    image: guestImage, // using existing imported image
  },
  {
    id: 5,
    name: "Observation 99",
    addedBy: "Jane Smith",
    addedOn: "2023-11-15",
    expirationDate: "2024-11-15",
    status: "Inactive",
    image: guestImage, // using existing imported image
  },
  {
    id: 6,
    name: "Observation 4",
    addedBy: "Jane Smith",
    addedOn: "2023-11-15",
    expirationDate: "2024-11-15",
    status: "Active",
    image: guestImage, // using existing imported image
  },
  {
    id: 7,
    name: "Observation 6",
    addedBy: "Jane Smith",
    addedOn: "2023-11-15",
    expirationDate: "2024-11-15",
    status: "Inactive",
    image: guestImage, // using existing imported image
  },
  {
    id: 8,
    name: "Observation 2",
    addedBy: "Jane Smith",
    addedOn: "2023-11-15",
    expirationDate: "2024-11-15",
    status: "Inactive",
    image: guestImage, // using existing imported image
  },
];
export const defaultGuests = [
  {
    guestName: "John Doe",
    relationship: "Friend",
    restraining: "No",
    email: "<EMAIL>",
    mobile: "************",
  },
  {
    guestName: "Jane Smith",
    relationship: "Family",
    restraining: "Yes",
    email: "<EMAIL>",
    mobile: "************",
  },
  {
    guestName: "nema Smith",
    relationship: "Family",
    restraining: "Yes",
    email: "<EMAIL>",
    mobile: "************",
  },
];
export const facilityData = [
  {
    id: 1,
    facility: "India Einath Bldg 5",
    siteID: 243,
    siteType: "Commercial",
    totalSF: 543,
    maxOcc: 543,
    address1: "Tech Park",
    address2: "Chennai",
    state: "Tamil Nadu",
    country: "India",
    postal: 600113,
    status: "Active",
  },
  {
    id: 2,
    facility: "India Einath Bldg 5",
    siteID: 456,
    siteType: "Data center",
    totalSF: 576,
    maxOcc: 576,
    address1: "Tech Park",
    address2: "Chennai",
    state: "Tamil Nadu",
    country: "India",
    postal: 600113,
    status: "Expired",
  },
  {
    id: 3,
    facility: "India Einath Bldg 5",
    siteID: 8657,
    siteType: "Commercial",
    totalSF: 312,
    maxOcc: 312,
    address1: "Tech Park",
    address2: "Chennai",
    state: "Tamil Nadu",
    country: "India",
    postal: 600113,
    status: "Expired",
  },
  {
    id: 4,
    facility: "India Einath Bldg 5",
    siteID: 435,
    siteType: "Educational",
    totalSF: 654,
    maxOcc: 654,
    address1: "Tech Park",
    address2: "Chennai",
    state: "Tamil Nadu",
    country: "India",
    postal: 600113,
    status: "Active",
  },
  {
    id: 5,
    facility: "India Einath Bldg 5",
    siteID: 87868,
    siteType: "Educational",
    totalSF: 765,
    maxOcc: 765,
    address1: "Tech Park",
    address2: "Chennai",
    state: "Tamil Nadu",
    country: "India",
    postal: 600113,
    status: "Active",
  },
  {
    id: 6,
    facility: "India Einath Bldg 5",
    siteID: 423,
    siteType: "Commercial",
    totalSF: 354,
    maxOcc: 354,
    address1: "Tech Park",
    address2: "Chennai",
    state: "Tamil Nadu",
    country: "India",
    postal: 600113,
    status: "Active",
  },
  {
    id: 7,
    facility: "India Einath Bldg 5",
    siteID: 423,
    siteType: "Data center",
    totalSF: 534,
    maxOcc: 534,
    address1: "Tech Park",
    address2: "Chennai",
    state: "Tamil Nadu",
    country: "India",
    postal: 600113,
    status: "Active",
  },
  {
    id: 8,
    facility: "India Einath Bldg 5",
    siteID: 784,
    siteType: "Commercial",
    totalSF: 456,
    maxOcc: 456,
    address1: "Tech Park",
    address2: "Chennai",
    state: "Tamil Nadu",
    country: "India",
    postal: 600113,
    status: "Expired",
  },
  {
    id: 1,
    facility: "India Einath Bldg 1",
    siteID: 724,
    siteType: "Commecial",
    totalSF: 45,
    maxOcc: 45,
    address1: "Park",
    address2: "Chennai",
    state: "Tamil Na2du",
    country: "India",
    postal: 6001123,
    status: "Expired",
  },
];
export const validationTask = [
  {
    id: 1,
    taskId: "1590",
    description: "Area Owner val",
    runId: "1655",
    taskOwner: "John Smith",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    rec: "ok",
    status: "complete",
  },
  {
    id: 2,
    taskId: "1591",
    description: "Gym Owner val",
    runId: "1655",
    taskOwner: "John Smith",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    rec: "ok",
    status: "complete",
  },
  {
    id: 3,
    taskId: "1592",
    description: "Ajj Owner val",
    runId: "1655",
    taskOwner: "John Smith",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    rec: "ok",
    status: "complete",
  },
  {
    id: 4,
    taskId: "1580",
    description: "Area dsoif val",
    runId: "1655",
    taskOwner: "John Smith",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    rec: "notOk",
    status: "pending",
  },
  {
    id: 5,
    taskId: "1581",
    description: "sdf Owner val",
    runId: "1655",
    taskOwner: "John Smith",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    rec: "ok",
    status: "pending",
  },
  {
    id: 6,
    taskId: "1597",
    description: "Aakas Owner val",
    runId: "1655",
    taskOwner: "John Smith",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    rec: "notOk",
    status: "complete",
  },
  {
    id: 7,
    taskId: "1490",
    description: "Kajs Owner val",
    runId: "1655",
    taskOwner: "John Smith",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    rec: "ok",
    status: "pending",
  },
  {
    id: 8,
    taskId: "1490",
    description: "Kajs Owner val",
    runId: "1655",
    taskOwner: "John Smith",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    rec: "ok",
    status: "pending",
  },
];
export const validationRun = [
  {
    id: 1,
    validationName: "Validation1",
    runId: "1655",
    validationType: "Access Area",
    assigned: "Area Owner",
    status: "Active",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    complete:"1655",
    pending:"55",
    total:"1655",
  },
  {
    id: 2,
    validationName: "Validation2",
    runId: "1655",
    validationType: "Access Area",
    assigned: "Area Owner",
    status: "Pending",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    complete:"1655",
    pending:"55",
    total:"1655",
  },
  {
    id: 3,
    validationName: "Validation3",
    runId: "1655",
    validationType: "Access Area",
    assigned: "Area Owner",
    status: "Active",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    complete:"1655",
    pending:"55",
    total:"1655",
  },
  {
    id: 4,
    validationName: "Validation4",
    runId: "1655",
    validationType: "Access Area",
    assigned: "Area Owner",
    status: "Active",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    complete:"1655",
    pending:"55",
    total:"1655",
  },
  {
    id: 5,
    validationName: "Validation5",
    runId: "1655",
    validationType: "Access Area",
    assigned: "Area Owner",
    status: "Active",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    complete:"1655",
    pending:"55",
    total:"1655",
  },
  {
    id: 6,
    validationName: "Validation6",
    runId: "1655",
    validationType: "Access Area",
    assigned: "Area Owner",
    status: "Pending",
    startDate: "09/04/2020",
    endDate: "12/04/2020",
    complete:"1655",
    pending:"55",
    total:"1655",
  },
  
];
export const Task = [
  {
    id: 1,
    item: "3808Test",
    identity: "John Doe",
    type: "Employee",
    company: "ATT",
    organization: "HR Divsion",
    sa:"Yes",
    lastAccess: "04/16/2025 13:05",
    recommendation: "ok",
    status:"complete",
    comment:"ok",
  },
  {
    id: 2,
    item: "7-Geo04-Dallas",
    identity: "845hjfsd",
    type: "Employee",
    company: "ATT",
    organization: "HR Divsion",
    sa:"Yes",
    lastAccess: "04/16/2025 13:05",
    recommendation: "ok",
    status:"pending",
    comment:"ok",
  },
  {
    id: 3,
    item: "552 Gibson Stream",
    identity: "sdf43",
    type: "Employee",
    company: "ATT",
    organization: "HR Divsion",
    sa:"Yes",
    lastAccess: "04/16/2025 13:05",
    recommendation: "notOk",
    status:"complete",
    comment:"ok",
  },
  {
    id: 4,
    item: "7-Geo04-Dallas",
    identity: "fge34",
    type: "Employee",
    company: "ATT",
    organization: "HR Divsion",
    sa:"Yes",
    lastAccess: "04/16/2025 13:05",
    recommendation: "ok",
    status:"complete",
    comment:"ok",
  },
  {
    id: 5,
    item: "3808Test",
    identity: "345dfg",
    type: "Employee",
    company: "ATT",
    organization: "HR Divsion",
    sa:"Yes",
    lastAccess: "04/16/2025 13:05",
    recommendation: "ok",
    status:"complete",
    comment:"ok",
  },
  {
    id: 6,
    item: "7-Geo04-Dallas-",
    identity: "97fc",
    type: "Employee",
    company: "ATT",
    organization: "HR Divsion",
    sa:"Yes",
    lastAccess: "04/16/2025 13:05",
    recommendation: "ok",
    status:"complete",
    comment:"ok",
  },
  
];
// Validation Configuration
export const ConfigurationData = [
  {
    id: 1,
    title: "Validation1",
    description: "Monthly Validation",
    type: "Area Owner",
    owner: "1655",
    status: "Active",
  },
  {
    id: 2,
    title: "Validation2",
    description: "Monthly Validation",
    type: "Area Owner",
    owner: "1655",
    status: "Pending",
  },
  {
    id: 3,
    title: "Validation3",
    description: "Monthly Validation",
    type: "Area Owner",
    owner: "1655",
    status: "Active",
  },
  {
    id: 4,
    title: "Validation4",
    description: "Monthly Validation",
    type: "Area Owner",
    owner: "1655",
    status: "Active",
  },
  {
    id: 5,
    title: "Validation5",
    description: "Monthly Validation",
    type: "Area Owner",
    owner: "1655",
    status: "Pending",
  },
  {
    id: 6,
    title: "Validation6",
    description: "Monthly Validation",
    type: "Area Owner",
    owner: "1655",
    status: "Active",
  },
  
];
export const AccessAreaData = [
  {
    id: 1,
    areaName: "North Wing",
    pacsAreaName: "PACS North",
    facility: "Facility A",
    system: "System X",
    online: "Yes",
    requestable: "Yes",
    areaTypes: "Type 1",
    cardTypes: "Card A",
    status: "Active",
  },
  {
    id: 2,
    areaName: "South Wing",
    pacsAreaName: "PACS South",
    facility: "Facility B",
    system: "System Y",
    online: "No",
    requestable: "Yes",
    areaTypes: "Type 2",
    cardTypes: "Card B",
    status: "Deleted",
  },
  {
    id: 3,
    areaName: "East Wing",
    pacsAreaName: "PACS East",
    facility: "Facility C",
    system: "System Z",
    online: "Yes",
    requestable: "No",
    areaTypes: "Type 3",
    cardTypes: "Card C",
    status: "Active",
  },
  {
    id: 4,
    areaName: "West Wing",
    pacsAreaName: "PACS West",
    facility: "Facility D",
    system: "System X",
    online: "No",
    requestable: "No",
    areaTypes: "Type 1",
    cardTypes: "Card D",
    status: "Active",
  },
  {
    id: 5,
    areaName: "Central Zone",
    pacsAreaName: "PACS Central",
    facility: "Facility E",
    system: "System Y",
    online: "Yes",
    requestable: "Yes",
    areaTypes: "Type 2",
    cardTypes: "Card E",
    status: "Deleted",
  },
];
export const patientHubData = [
  {
    id: 1,
    name: "John Doe",
    mrn: "12345",
    type: "Inpatient",
    admission: "2025-01-01",
    discharge: "2025-01-10",
    facility: "General Hospital",
    building: "A",
    floor: "2",
    room: "205",
    deptUnit: "Cardiology",
    lastSync: "2025-01-15",
    status: "Admitted",
  },
  {
    id: 2,
    name: "Jane Smith",
    mrn: "67890",
    type: "Outpatient",
    admission: "2025-01-12",
    discharge: "",
    facility: "Specialty Clinic",
    building: "B",
    floor: "1",
    room: "102",
    deptUnit: "Dermatology",
    lastSync: "2025-01-18",
    status: "Admitted",
  },
  {
    id: 3,
    name: "Jane ",
    mrn: "890",
    type: "Outpatient",
    admission: "2025-01-12",
    discharge: "2025-01-12",
    facility: " Clinic",
    building: "B",
    floor: "1",
    room: "102",
    deptUnit: "Dermatology",
    lastSync: "2025-01-18",
    status: "Discharged",
  },
  {
    id: 4,
    name: " Smith",
    mrn: "62902",
    type: "Outpatient",
    admission: "2025-02-12",
    discharge: "2025-03-12",
    facility: "Specialty",
    building: "B",
    floor: "1",
    room: "102",
    deptUnit: "Dermatology",
    lastSync: "2025-01-18",
    status: "Discharged",
  },
  {
    id: 5,
    name: " scamith",
    mrn: "629320",
    type: "Outpatient",
    admission: "2025-02-12",
    discharge: "2025-03-12",
    facility: "Specialtw",
    building: "B",
    floor: "1",
    room: "102",
    deptUnit: "Dermatology",
    lastSync: "2025-01-18",
    status: "Discharged",
  },
  {
    id: 6,
    name: " asdmith",
    mrn: "612290",
    type: "Outpatient",
    admission: "2025-02-12",
    discharge: "2025-03-12",
    facility: " WClinic",
    building: "B",
    floor: "1",
    room: "102",
    deptUnit: "Dermatology",
    lastSync: "2025-01-18",
    status: "Admitted",
  },
  {
    id: 7,
    name: " wsmith",
    mrn: "6123290",
    type: "Outpatient",
    admission: "2025-02-12",
    discharge: "2025-03-12",
    facility: "Specialtyew",
    building: "B",
    floor: "1",
    room: "102",
    deptUnit: "Dermatology",
    lastSync: "2025-01-18",
    status: "Discharged",
  },
  {
    id: 8,
    name: "ith",
    mrn: "629320",
    type: "Outpatient",
    admission: "2025-02-12",
    discharge: "2025-03-12",
    facility: "EwdClinic",
    building: "B",
    floor: "1",
    room: "102",
    deptUnit: "Dermatology",
    lastSync: "2025-01-18",
    status: "Admitted",
  },
];
export const AddOwnersData = [
  {
    id: 1,
    name: "John Doe",
    eid: "E001",
    type: "Employee",
    organization: "Org1",
    jobTitle: "Developer",
    status: "Active",
  },
  {
    id: 2,
    name: "Jane Smith",
    eid: "E002",
    type: "Employee",
    organization: "Org1",
    jobTitle: "Designer",
    status: "Inactive",
  },
  {
    id: 3,
    name: "Alice Johnson",
    eid: "E003",
    type: "Manager",
    organization: "Org2",
    jobTitle: "Project Manager",
    status: "Active",
  },
  {
    id: 4,
    name: "Bob Brown",
    eid: "E004",
    type: "Employee",
    organization: "Org2",
    jobTitle: "QA Engineer",
    status: "Active",
  },
  {
    id: 5,
    name: "Charlie Green",
    eid: "E005",
    type: "Employee",
    organization: "Org3",
    jobTitle: "Support",
    status: "Inactive",
  },
];
export const AddApproversData = [
  {
    id: 1,
    name: "Emma Wilson",
    eid: "A101",
    approverLevel: "Level 1",
    type: "Manager",
    organization: "Org1",
    jobTitle: "Supervisor",
    startDate: "2023-01-01",
    endDate: "2023-12-31",
    status: "Active",
  },
  {
    id: 2,
    name: "Liam Davis",
    eid: "A102",
    approverLevel: "Level 2",
    type: "Senior Manager",
    organization: "Org2",
    jobTitle: "Team Lead",
    startDate: "2023-02-01",
    endDate: "2023-11-30",
    status: "Inactive",
  },
  {
    id: 3,
    name: "Olivia Martinez",
    eid: "A103",
    approverLevel: "Level 1",
    type: "Manager",
    organization: "Org3",
    jobTitle: "Coordinator",
    startDate: "2023-03-01",
    endDate: "2023-10-31",
    status: "Active",
  },
  {
    id: 4,
    name: "Noah Anderson",
    eid: "A104",
    approverLevel: "Level 3",
    type: "Director",
    organization: "Org1",
    jobTitle: "Director",
    startDate: "2023-04-01",
    endDate: "2023-09-30",
    status: "Active",
  },
  {
    id: 5,
    name: "Sophia Thomas",
    eid: "A105",
    approverLevel: "Level 2",
    type: "Manager",
    organization: "Org2",
    jobTitle: "Project Lead",
    startDate: "2023-05-01",
    endDate: "2023-08-31",
    status: "Inactive",
  },
];
export const AssignedIdentitiesData = [
  {
    id: 1,
    name: "John Doe",
    eid: "E001",
    type: "Employee",
    company: "Company A",
    organization: "Org1",
    jobTitle: "Developer",
    endDate: "2023-12-31",
    status: "Active",
  },
  {
    id: 2,
    name: "Jane Smith",
    eid: "E002",
    type: "Manager",
    company: "Company B",
    organization: "Org2",
    jobTitle: "Team Lead",
    endDate: "2023-11-30",
    status: "Inactive",
  },
  {
    id: 3,
    name: "Alice Johnson",
    eid: "E003",
    type: "Employee",
    company: "Company A",
    organization: "Org1",
    jobTitle: "Designer",
    endDate: "2023-10-15",
    status: "Active",
  },
  {
    id: 4,
    name: "Bob Brown",
    eid: "E004",
    type: "Director",
    company: "Company C",
    organization: "Org3",
    jobTitle: "Project Manager",
    endDate: "2023-09-30",
    status: "Active",
  },
  {
    id: 5,
    name: "Charlie Green",
    eid: "E005",
    type: "Employee",
    company: "Company D",
    organization: "Org4",
    jobTitle: "QA Engineer",
    endDate: "2023-08-31",
    status: "Active",
  },
];
export const AreasData = [
  { id: 1, areaName: "Area 101", areaType: "Office", system: "HVAC" },
  { id: 2, areaName: "Area 102", areaType: "Conference", system: "Lighting" },
  { id: 3, areaName: "Area 201", areaType: "Lab", system: "Security" },
  { id: 4, areaName: "Area 202", areaType: "Storage", system: "Fire" },
  { id: 5, areaName: "Area 301", areaType: "Office", system: "HVAC" },
  { id: 6, areaName: "Area 302", areaType: "Break Room", system: "Cafeteria" },
  { id: 7, areaName: "Area 401", areaType: "Meeting", system: "Audio" },
  { id: 8, areaName: "Area 402", areaType: "Office", system: "IT" },
];
export const AccessAreaGroupData = [
  {
    id: 1,
    areaGroupName: "Group Alpha",
    type: "Type 1",
    creation: "2023-01-15",
    requestable: "Yes",
    status: "Active",
  },
  {
    id: 2,
    areaGroupName: "Group Beta",
    type: "Type 2",
    creation: "2023-02-10",
    requestable: "No",
    status: "Deleted",
  },
  {
    id: 3,
    areaGroupName: "Group Gamma",
    type: "Type 1",
    creation: "2023-03-05",
    requestable: "Yes",
    status: "Active",
  },
  {
    id: 4,
    areaGroupName: "Group Delta",
    type: "Type 3",
    creation: "2023-01-20",
    requestable: "No",
    status: "Active",
  },
  {
    id: 5,
    areaGroupName: "Group Epsilon",
    type: "Type 2",
    creation: "2023-02-28",
    requestable: "Yes",
    status: "Deleted",
  },
  {
    id: 6,
    areaGroupName: "Group Zeta",
    type: "Type 3",
    creation: "2023-03-15",
    requestable: "Yes",
    status: "Active",
  },
  {
    id: 7,
    areaGroupName: "Group Eta",
    type: "Type 1",
    creation: "2023-04-01",
    requestable: "No",
    status: "Active",
  },
  {
    id: 8,
    areaGroupName: "Group Theta",
    type: "Type 2",
    creation: "2023-04-15",
    requestable: "Yes",
    status: "Deleted",
  },
];
export const GuestListData = [
  {
    id: 1,
    visitorsName: "John Doe",
    checkIn: "2025-01-20 10:00 AM",
    checkOut: "2025-01-20 02:00 PM",
    facility: "Gym",
    building: "Main Building",
    floor: "2nd Floor",
    room: "202",
    status: "Checked Out",
  },
  {
    id: 2,
    visitorsName: "Jane Smith",
    checkIn: "2025-01-21 09:30 AM",
    checkOut: "2025-01-21 12:00 PM",
    facility: "Swimming Pool",
    building: "East Wing",
    floor: "1st Floor",
    room: "101",
    status: "Checked Out",
  },
  {
    id: 3,
    visitorsName: "Alice Johnson",
    checkIn: "2025-01-22 11:00 AM",
    checkOut: "2025-01-22 01:30 PM",
    facility: "Spa",
    building: "West Wing",
    floor: "Ground Floor",
    room: "G05",
    status: "Checked In",
  },
  {
    id: 4,
    visitorsName: "Robert Brown",
    checkIn: "2025-01-23 08:00 AM",
    checkOut: "2025-01-23 11:00 AM",
    facility: "Conference Room",
    building: "Annex",
    floor: "3rd Floor",
    room: "305",
    status: "Checked Out",
  },
  {
    id: 5,
    visitorsName: "Emily Davis",
    checkIn: "2025-01-24 01:00 PM",
    checkOut: "2025-01-24 03:00 PM",
    facility: "Lounge",
    building: "Main Building",
    floor: "4th Floor",
    room: "404",
    status: "Checked In",
  },
];
export const FriendsFamilyData = [
  {
    id: 1,
    name: "John Doe",
    relationship: "Friend",
    email: "<EMAIL>",
    phone: "**********",
  },
  {
    id: 2,
    name: "Jane Smith",
    relationship: "Family",
    email: "<EMAIL>",
    phone: "0987654321",
  },
  {
    id: 3,
    name: "Mike Johnson",
    relationship: "Friend",
    email: "<EMAIL>",
    phone: "1122334455",
  },
  {
    id: 4,
    name: "Emily Davis",
    relationship: "Family",
    email: "<EMAIL>",
    phone: "6677889900",
  },
];
export const DeniedGuestsData = [
  {
    id: 1,
    guestName: "John Doe",
    denialReason: "Incomplete registration",
    contactEmail: "<EMAIL>",
    contactNumber: "**********",
    dateDenied: "2025-02-01",
  },
  {
    id: 2,
    guestName: "Jane Smith",
    denialReason: "Invalid ID",
    contactEmail: "<EMAIL>",
    contactNumber: "0987654321",
    dateDenied: "2025-02-03",
  },
  {
    id: 3,
    guestName: "Mike Johnson",
    denialReason: "Late arrival",
    contactEmail: "<EMAIL>",
    contactNumber: "1122334455",
    dateDenied: "2025-02-05",
  },
  {
    id: 4,
    guestName: "Emily Davis",
    denialReason: "Dress code violation",
    contactEmail: "<EMAIL>",
    contactNumber: "6677889900",
    dateDenied: "2025-02-07",
  },
];
export const TIME_ZONES = [
  "UTC-12:00",
  "UTC-11:00",
  "UTC-10:00",
  "UTC-09:00",
  "UTC-08:00",
  "UTC-07:00",
  "UTC-06:00",
  "UTC-05:00",
  "UTC-04:00",
  "UTC-03:00",
  "UTC-02:00",
  "UTC-01:00",
  "UTC+00:00",
  "UTC+01:00",
  "UTC+02:00",
  "UTC+03:00",
  "UTC+04:00",
  "UTC+05:00",
  "UTC+06:00",
  "UTC+07:00",
  "UTC+08:00",
  "UTC+09:00",
  "UTC+10:00",
  "UTC+11:00",
  "UTC+12:00",
];
export const STATUSES = ["Active", "Inactive"];


// Request Hub
export const RequestHubData=[
  {
    id: 1,
    requestId: "51624",
    type: "New Card Request",
    requestFor: "Soni awadhesh",
    items: "122",
    justification: "New Joine",
    startDate: "09/04/2020",
    endDate: "10/04/2020",
    requestedBy: "John",
    createdOn: "2024-01-31",
    status: "Active",
  },
  {
    id: 2,
    requestId: "56624",
    type: "New Card Request",
    requestFor: "Soni awadhesh",
    items: "43",
    justification: "New Joine",
    startDate: "09/04/2020",
    endDate: "10/04/2020",
    requestedBy: "John",
    createdOn: "2024-01-31",
    status: "Pending",
  },
  {
    id: 3,
    requestId: "74635",
    type: "New Card Request",
    requestFor: "Soni awadhesh",
    items: "95",
    justification: "New Joine",
    startDate: "09/04/2020",
    endDate: "10/04/2020",
    requestedBy: "John",
    createdOn: "2024-01-31",
    status: "Active",
  },
  {
    id: 4,
    requestId: "324567",
    type: "New Card Request",
    requestFor: "Soni awadhesh",
    items: "633",
    justification: "New Joine",
    startDate: "09/04/2020",
    endDate: "10/04/2020",
    requestedBy: "John",
    createdOn: "2024-01-31",
    status: "Pending",
  },
  {
    id: 5,
    requestId: "32435643",
    type: "New Card Request",
    requestFor: "Soni awadhesh",
    items: "142",
    justification: "New Joine",
    startDate: "09/04/2020",
    endDate: "10/04/2020",
    requestedBy: "John",
    createdOn: "2024-01-31",
    status: "Active",
  },
]

export const RequestAreaData = [
  { area: "Zone 1", facility: "Geo08", address: "HSR", city: "Jaipur", state: "Rajasthan", country: "India" },
  { area: "Zone 2", facility: "Geo08", address: "HSR", city: "Jaipur", state: "Rajasthan", country: "India" },
  { area: "Zone 3", facility: "Geo08", address: "HSR", city: "Jaipur", state: "Rajasthan", country: "India" },
  { area: "Zone 4", facility: "Geo08", address: "HSR", city: "Jaipur", state: "Rajasthan", country: "India" },
];

export const RequestApprovalsData = [
  {
      id: "1",
      area: "Zone 1",
      approval: "Alice",
      taskId: "T44116",
      taskOwner: "Ronald Low",
      taskSetOn: "31-03-2020",
      approver: "Sally Smith",
      responseDate: "31-03-2020",
      comment: "Hi Everyone",
      status: "Active",
  },
  {
      id: "2",
      area: "Zone 1",
      approval: "Alice",
      taskId: "T44116",
      taskOwner: "Ronald Low",
      taskSetOn: "31-03-2020",
      approver: "Sally Smith",
      responseDate: "31-03-2020",
      comment: "Hi Everyone",
      status: "Pending",
  },
  {
      id: "3",
      area: "Zone 1",
      approval: "Alice",
      taskId: "T44116",
      taskOwner: "Ronald Low",
      taskSetOn: "31-03-2020",
      approver: "Sally Smith",
      responseDate: "31-03-2020",
      comment: "Hi Everyone",
      status: "Active",
  },
  {
      id: "4",
      area: "Zone 1",
      approval: "Alice",
      taskId: "T44116",
      taskOwner: "Ronald Low",
      taskSetOn: "31-03-2020",
      approver: "Sally Smith",
      responseDate: "31-03-2020",
      comment: "Hi Everyone",
      status: "Active",
  },
];

// Task Hub
export const initialTasks = [
  {
    id: 1,
    taskId: "2345",
    type: "Card Issue",
    requestFor: "Soni",
    items: "Multitech",
    requestId: "7655",
    requestedBy: "John",
    createdBy: "Admin",
    assignee: "Badge",
    justification: "New Employee",
    recommend: "Approve",
  },
  {
    id: 2,
    taskId: "65432",
    type: "Card Issue",
    requestFor: "Soni",
    items: "Multitech",
    requestId: "7655",
    requestedBy: "John",
    createdBy: "Admin",
    assignee: "Badge",
    justification: "New Employee",
    recommend: "Approve",
  },
  {
    id: 3,
    taskId: "324524",
    type: "Card Issue",
    requestFor: "Soni",
    items: "Multitech",
    requestId: "7653245",
    requestedBy: "John",
    createdBy: "Admin",
    assignee: "Badge",
    justification: "New Employee",
    recommend: "Approve",
  },
  {
    id: 4,
    taskId: "356223",
    type: "Card Issue",
    requestFor: "Soni",
    items: "Multitech",
    requestId: "435",
    requestedBy: "John",
    createdBy: "Admin",
    assignee: "Badge",
    justification: "New Employee",
    recommend: "Approve",
  },
  {
    id: 5,
    taskId: "67543",
    type: "Card Issue",
    requestFor: "Soni",
    items: "Multitech",
    requestId: "342",
    requestedBy: "John",
    createdBy: "Admin",
    assignee: "Badge",
    justification: "New Employee",
    recommend: "Approve",
  },
  {
      id: 6,
      taskId: "2345",
      type: "Card Issue",
      requestFor: "Soni",
      items: "Multitech",
      requestId: "76545",
      requestedBy: "John",
      createdBy: "Admin",
      assignee: "Badge",
      justification: "New Employee",
      recommend: "Deny",
    },
];
export const TaskAreaData = [
  { area: "Zone 1", facility: "Geo08",geoLoc:"T44116", address: "HSR", city: "Jaipur", state: "Rajasthan", country: "India",schedule:"Default" },
  { area: "Zone 2", facility: "Geo08",geoLoc:"T44116", address: "HSR", city: "Jaipur", state: "Rajasthan", country: "India",schedule:"Default" },
  { area: "Zone 3", facility: "Geo08",geoLoc:"T44116", address: "HSR", city: "Jaipur", state: "Rajasthan", country: "India",schedule:"Default" },
  { area: "Zone 4", facility: "Geo08",geoLoc:"T44116", address: "HSR", city: "Jaipur", state: "Rajasthan", country: "India",schedule:"Default" },
];

export const TaskApprovalsData = [
  {
      id: "1",
      area: "Zone 1",
      approval: "Alice",
      taskId: "T44116",
      taskOwner: "Ronald Low",
      taskSetOn: "31-03-2020",
      approver: "Sally Smith",
      responseDate: "31-03-2020",
      comment: "Hi Everyone",
      status: "Active",
  },
  {
      id: "2",
      area: "Zone 1",
      approval: "Alice",
      taskId: "T44116",
      taskOwner: "Ronald Low",
      taskSetOn: "31-03-2020",
      approver: "Sally Smith",
      responseDate: "31-03-2020",
      comment: "Hi Everyone",
      status: "Pending",
  },
  {
      id: "3",
      area: "Zone 1",
      approval: "Alice",
      taskId: "T44116",
      taskOwner: "Ronald Low",
      taskSetOn: "31-03-2020",
      approver: "Sally Smith",
      responseDate: "31-03-2020",
      comment: "Hi Everyone",
      status: "Active",
  },
  {
      id: "4",
      area: "Zone 1",
      approval: "Alice",
      taskId: "T44116",
      taskOwner: "Ronald Low",
      taskSetOn: "31-03-2020",
      approver: "Sally Smith",
      responseDate: "31-03-2020",
      comment: "Hi Everyone",
      status: "Active",
  },
];

export const setIdentityDataa = [
      {
        id: 1,
        name: "Randalll Moran",
        eid: "CA4433335",
        type: "COS",
        company: "Conesco",
        organization: "Mohan",
        jobTitle: "Engineer",
        endDate: "Jan-31-2024",
        status: "Active",
      },
      {
        id: 2,
        name: "Edith Stewart",
        eid: "CA4443335",
        type: "EMP",
        company: "Conesco",
        organization: "Mohan",
        jobTitle: "Analyst",
        endDate: "Jan-31-2024",
        status: "Inactive",
      },
      {
        id: 3,
        name: "Edith Stewart",
        eid: "CA4443336",
        type: "EMP",
        company: "Conesco",
        organization: "Mohan",
        jobTitle: "Analyst",
        endDate: "Jan-31-2024",
        status: "Terminated",
      },
      {
        id: 4,
        name: "Edith Stewart",
        eid: "CA4443337",
        type: "EMP",
        company: "Conesco",
        organization: "Mohan",
        jobTitle: "Analyst",
        endDate: "Jan-31-2024",
        status: "Suspended",
      },
    ]

    export const setCardsDataa=[
      {
        id: 101,
        cardNumber: "CARD-0001",
        cardType: "Standard",
        owner: "CA4433335",
        expiry: "Dec-31-2024",
        status: "Active",
      },
      {
        id: 102,
        cardNumber: "CARD-0002",
        cardType: "VIP",
        owner: "CA4443335",
        expiry: "Mar-31-2025",
        status: "Pending activation",
      },
      {
        id: 103,
        cardNumber: "CARD-0003",
        cardType: "Standard",
        owner: "CA4443336",
        expiry: "Jan-31-2025",
        status: "Pending assignment",
      },
    ]
export const setAccessData=[
  {
    id: 201,
    areaName: "North Wing",
    pacsAreaName: "PACS North",
    facility: "Facility A",
    system: "System X",
    online: "Yes",
    requestable: "Yes",
    areaTypes: "Type 1",
    cardTypes: "Card A",
    status: "All",
  },
  {
    id: 202,
    areaName: "South Wing",
    pacsAreaName: "PACS South",
    facility: "Facility B",
    system: "System Y",
    online: "No",
    requestable: "Yes",
    areaTypes: "Type 2",
    cardTypes: "Card B",
    status: "Assignment",
  },
  {
    id: 203,
    areaName: "East Wing",
    pacsAreaName: "PACS East",
    facility: "Facility C",
    system: "System Z",
    online: "Yes",
    requestable: "No",
    areaTypes: "Type 3",
    cardTypes: "Card C",
    status: "Pending",
  },
]    

