import In from "../Images/in.svg";
import <PERSON><PERSON> from "../Images/camera.svg";
import Chat from "../Images/chat.svg";
import Print from "../Images/print.svg";
import Out from "../Images/out.svg";
import React from "react";
import TruncatedCell from "../Components/Tooltip/TruncatedCell";
import { checkinGuestForVisit, checkoutGuestForVisit } from "./visitor-hub";
import formatDateTime from "../utils/formatDateTime";
// import Cemara from "../../Images/camera.svg";
// import Chat from "../../Images/chat.svg";
// import Print from "../../Images/print.svg";
// import In from "../../Images/in.svg";
// import Out from "../../Images/out.svg";

/**
 * Returns the column configuration for the Visitor table.
 *
 * @param {Object} params
 * @param {Function} params.openModal - Handler to open the modal (used in Guest Action column)
 * @param {Function} params.handlePrintClick - Handler for print action
 * @param {string} params.profileImage - Fallback image URL if none is provided in the data
 */
export const getVisitorColumns = ({ openModal, handlePrintClick, profileImage, onRefresh }) => [
  {
    name: "Name",
    id: "guest_name",
    selector: (row) => row.guest_name || row.visitorName || "N/A",
    cell: (row) => (
      <div className="flex items-center">
        <img
          src={row.image || profileImage}
          alt={row.visitorName || row.guest_name}
          className="w-8 h-8 rounded-full mr-2"
        />
        <span>{row.guest_name || row.visitorName}</span>
      </div>
    ),
    sortable: true,
  },
  {
    name: "Host",
    id: "host_name",
    selector: (row) => row.host_name || row.hostName || "N/A",
    sortable: true,
  },
  {
    name: "Facility",
    id: "facility_name",
    selector: (row) => row.facility_name || row.facility || "N/A",
    sortable: true,
  },
  {
    name:<TruncatedCell text= "Start Date & Time"/>,
    id: "check_in_time",
    selector: (row) => formatDateTime(row.check_in_time || row.startDate),
    sortable: true,
  },
  {
    name:<TruncatedCell text= "End Date & Time"/>,
    id: "check_out_time",
    selector: (row) => formatDateTime(row.check_out_time || row.endDate),
    sortable: true,
  },
  {
    name: "Guest Action",
    cell: (row) => (
      <div className="flex justify-center items-center space-x-2">
        <img
          src={Cemara}
          alt="Camera"
          className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
          onClick={() => openModal("Update Profile Image", row.id)}
        />
        <img
          src={Chat}
          alt="Chat"
          className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
          onClick={() => console.log("Chat clicked for:", row.visitorName)}
        />
        <img
          src={Print}
          alt="Print"
          className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
          onClick={() => handlePrintClick(row)}
        />
        <img
          src={In}
          alt="In"
          className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
          onClick={async () => {
            await checkinGuestForVisit(row.visit_id, row.guest_id);
            onRefresh?.();
          }}
        />
        <img
          src={Out}
          alt="Out"
          className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
          onClick={async () => {
            await checkoutGuestForVisit(row.visit_id, row.guest_id);
            onRefresh?.();
          }}
        />
      </div>
    ),
    center: true,
  },
];

export const TodayVisitorsColumns  = ({ openModal, handlePrintClick, profileImage, onRefresh }) => [
    {
        name: "Visitor Name",
        cell: (row) => (
            <div className="flex items-center">
                <img
                    src={row.image || profileImage}
                    alt={row.visitorName}
                    className="w-8 h-8 rounded-full mr-2"
                />
                <span>{row.visitorName}</span>
            </div>
        ),
        sortable: true,
    },
    {
        name: "Host",
        selector: (row) => row.visitorHost || "N/A",
    },
    {
        name: "Facility",
        selector: (row) => row.facility || "N/A",
    },
    {
        name: "Start Date & Time",
        selector: (row) => row.startDate || "N/A",
    },
    {
        name: "End Date & Time",
        selector: (row) => row.endDate || "N/A",
    },
    {
        name: "Action",
        cell: (row) => (
            <div className="flex justify-center items-center space-x-2">
                <img
                    src={Cemara}
                    alt="Camera"
                    className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                    onClick={() => openModal("Update Profile Image", row.id)}
                />
                <img
                    src={Chat}
                    alt="Chat"
                    className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                    onClick={() => console.log("Chat clicked for:", row)}
                />
                <img
                    src={Print}
                    alt="Print"
                    className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                    onClick={() => handlePrintClick(row)}
                />
                <img
                    src={In}
                    alt="In"
                    className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                    onClick={async () => {
                        await checkinGuestForVisit(row.visit_id, row.guest_id);
                        onRefresh?.();
                    }}
                />
                <img
                    src={Out}
                    alt="Out"
                    className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
                    onClick={async () => {
                        await checkoutGuestForVisit(row.visit_id, row.guest_id);
                        onRefresh?.();
                    }}
                />
            </div>
        ),
        center: true,
    },
];


