import api from "./";

/**
 * Create a visit with guest.
 *
 * @param {object} visitData - The data for the new visit with guest.
 * @param {string} visitData.facility_id - Facility ID
 * @param {string} visitData.host_id - Host ID
 * @param {string} visitData.escort_id - Escort ID (optional)
 * @param {string} visitData.start_date - Start date in YYYY-MM-DD format
 * @param {string} visitData.start_time - Start time in HH:MM format
 * @param {number} visitData.duration - Duration in hours
 * @param {Array} visitData.guests - Array of guest information
 * @returns {Promise<any>} A promise that resolves to the created visit data.
 */
export const createVisitWithGuest = async (visitData) => {
  try {
    console.log("Creating visit with guest:", visitData);

    // Validate required fields
    if (!visitData.facility_id) {
      throw new Error("Facility ID is required");
    }

    if (!visitData.host_id) {
      throw new Error("Host ID is required");
    }

    if (!visitData.start_date || !visitData.start_time || !visitData.duration) {
      throw new Error("Start date, start time, and duration are required");
    }

    if (!visitData.guests || !visitData.guests.length) {
      throw new Error("At least one guest is required");
    }

    const response = await api.post("/visits/create-with-guest", visitData);
    console.log("Visit created successfully:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error creating visit with guest:", error);

    // Handle different types of errors
    if (error.response) {
      // Server responded with error status
      const errorMessage = error.response.data?.message || error.response.data?.error || "Server error occurred";
      throw new Error(`API Error: ${errorMessage}`);
    } else if (error.request) {
      // Request was made but no response received
      throw new Error("Network error: Unable to reach the server");
    } else {
      // Something else happened
      throw error;
    }
  }
};

/**
 * Check in a guest for a specific visit.
 *
 * @param {string} visitId - The ID of the visit.
 * @param {string} guestId - The ID of the guest.
 * @returns {Promise<any>} A promise that resolves to the check-in response.
 */
export const checkinGuestForVisit = async (visitId, guestId) => {
  try {
    console.log("Checking in guest - visitId:", visitId, "guestId:", guestId);

    if (!visitId || !guestId) {
      throw new Error("Both visit ID and guest ID are required");
    }

    const response = await api.post(`/visits/${visitId}/guest/${guestId}/checkin`);
    console.log("Guest checked in successfully:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error checking in guest:", error);

    if (error.response) {
      const errorMessage = error.response.data?.message || error.response.data?.error || "Check-in failed";
      throw new Error(`Check-in Error: ${errorMessage}`);
    } else if (error.request) {
      throw new Error("Network error: Unable to check in guest");
    } else {
      throw error;
    }
  }
};


/**
 * Get guests for a specific visit in a facility.
 *
 * @param {string} facilityId - The ID of the facility.
 * @param {string} visitId - The ID of the visit (optional).
 * @returns {Promise<any>} A promise that resolves to the list of guests.
 */
export const getVisitGuests = async (facilityId, visitId) => {
  try {
    console.log("Getting visit guests - facilityId:", facilityId, "visitId:", visitId);

    if (!facilityId) {
      throw new Error("Facility ID is required");
    }

    // Construct the URL based on whether visitId is provided
    let url = `/visits/facility/${facilityId}/guests`;
    if (visitId) {
      url = `/visits/facility/${facilityId}/visit/${visitId}/guests`;
    }

    const response = await api.get(url);
    console.log("Retrieved guests successfully:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error getting visit guests:", error);

    // Handle different types of errors
    if (error.response) {
      // Server responded with error status
      const errorMessage = error.response.data?.message || error.response.data?.error || "Server error occurred";
      throw new Error(`API Error: ${errorMessage}`);
    } else if (error.request) {
      // Request was made but no response received
      throw new Error("Network error: Unable to reach the server");
    } else {
      // Something else happened
      throw error;
    }
  }
};





/**
 * Check out a guest for a specific visit.
 *
 * @param {string} visitId - The ID of the visit.
 * @param {string} guestId - The ID of the guest.
 * @returns {Promise<any>} A promise that resolves to the checkout response.
 */
export const checkoutGuestForVisit = async (visitId, guestId) => {
  try {
    console.log("Checking out guest - visitId:", visitId, "guestId:", guestId);

    if (!visitId || !guestId) {
      throw new Error("Both visit ID and guest ID are required");
    }

    const response = await api.post(`/visits/${visitId}/guest/${guestId}/checkout`);
    console.log("Guest checked out successfully:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error checking out guest:", error);

    if (error.response) {
      const errorMessage = error.response.data?.message || error.response.data?.error || "Check-out failed";
      throw new Error(`Check-out Error: ${errorMessage}`);
    } else if (error.request) {
      throw new Error("Network error: Unable to check out guest");
    } else {
      throw error;
    }
  }
};
