import api from "./";

/**
 * Creates a new person of interest (watchlist entry).
 *
 * @param {object} entryData - The data for the new watchlist entry.
 * @returns {Promise<any>} A promise that resolves to the newly created entry.
 */
export const createWatchlist = async (entryData) => {
  const response = await api.post("watchlist", entryData);
  return response.data;
};

/**
 * Retrieves all persons of interest with optional searching and sorting.
 *
 * @param {object} params - Query parameters for filtering, searching, or pagination.
 * @param {string} [params.search] - The search term to filter results.
 * @param {string} [params.sortBy] - The field to sort by.
 * @param {string} [params.sortOrder] - The sort order (e.g., "ASC" or "DESC").
 * @returns {Promise<any>} A promise that resolves to the list of entries.
 */
export const getWatchlists = async (params = {}) => {
  const { search, sortBy, sortOrder, ...rest } = params;
  const queryParams = {
    ...rest,
    ...(search ? { search } : {}),
    ...(sortBy ? { sortBy } : {}),
    ...(sortOrder ? { sortOrder } : {}),
  };
  const response = await api.get("watchlist", { params: queryParams });
  return response.data;
};

/**
 * Retrieves details of a specific watchlist entry.
 *
 * @param {object} params - Query parameters to identify the entry (e.g., { id }).
 * @returns {Promise<any>} A promise that resolves to the entry details.
 */
export const getWatchlistDetails = async (params = {}) => {
  const response = await api.get("watchlist/details", { params });
  return response.data;
};

/**
 * Retrieves a person of interest by ID.
 *
 * @param {string} id - The unique identifier of the watchlist entry.
 * @returns {Promise<any>} A promise that resolves to the entry data.
 */
export const getWatchlistById = async (id) => {
  const response = await api.get(`watchlist/${id}`);
  return response.data;
};

/**
 * Updates a person of interest.
 *
 * @param {string} id - The unique identifier of the entry to update.
 * @param {object} updateData - The data to update on the entry.
 * @returns {Promise<any>} A promise that resolves to the updated entry.
 */
export const updateWatchlist = async (id, updateData) => {
  const response = await api.put(`watchlist/${id}`, updateData);
  return response.data;
};

/**
 * Deletes a person of interest.
 *
 * @param {string} id - The unique identifier of the entry to delete.
 * @returns {Promise<any>} A promise that resolves when deletion is confirmed.
 */
export const deleteWatchlist = async (id) => {
  const response = await api.delete(`watchlist/${id}`);
  return response.data;
};

/**
 * Retrieves history for a specific watchlist entry.
 *
 * @param {string} watchlistId - The ID of the watchlist entry.
 * @returns {Promise<any>} A promise that resolves to the history records.
 */
export const getWatchlistHistoryById = async (watchlistId) => {
  const response = await api.get(`watchlist/history/${watchlistId}`);
  return response.data;
};


/**
 * Updates the image for a patient.
 *
 * @param {string} watchlistId - The ID of the patient.
 * @param {object} data - The data containing the new image URL.
 * @returns {Promise<any>} A promise that resolves to the updated patient image details.
 */
export const updateWatchlistImage = async (watchlistId, data) => {
    const response = await api.patch(`watchlist/${watchlistId}/image`, data);
    return response.data;
};


/**
 * Retrieves all watchlist history records, optionally filtered.
 *
 * @param {object} params - Optional query parameters (e.g., pagination).
 * @returns {Promise<any>} A promise that resolves to the list of history records.
 */
export const getWatchlistHistory = async (params = {}) => {
  const response = await api.get("watchlist/history", { params });
  return response.data;
};

/**
 * Deletes a specific watchlist document by its ID.
 *
 * @param {string} documentId - The unique identifier of the document to delete.
 * @returns {Promise<any>} A promise that resolves when deletion is confirmed.
 */
export const deleteWatchlistDocument = async (documentId) => {
  const response = await api.delete(`watchlist/document/${documentId}`);
  return response.data;
};
