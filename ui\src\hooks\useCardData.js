import { useState, useCallback, useEffect, useMemo } from "react";
import { getMasterData } from "../api/global";
import { toast } from "react-toastify";
import { toLookupMap } from "../utils/transformOptions";

let cardDataCache = null;

export const useCardData = () => {
  const [masterData, setMasterData] = useState(
    cardDataCache || {
      card_status: [],
      card_format: [],
      card_template: [],
    }
  );
  const fetchMasterData = useCallback(async () => {
    if (cardDataCache) return;
    try {
      const res = await getMasterData({
        groups: ["card_status", "card_format", "card_template"],
      });
      cardDataCache = res.data;
      setMasterData(res.data);
    } catch (error) {
      toast.error("Error fetching building master data");
    }
  }, []);

  useEffect(() => {
    if (!cardDataCache) {
      fetchMasterData();
    }
  }, [fetchMasterData]);

  // Prepare dropdown options using memoization
  const statusOptions = useMemo(() => {
    return masterData.card_status.map((item) => ({
      label: item.value,
        value: parseInt(item.key, 10),
    }));
  }, [masterData.card_status]);

  const formatOptions = useMemo(() => {
    return masterData.card_format.map((item) => ({
      label: item.value,
       value: parseInt(item.key, 10),
    }));
  }, [masterData.card_format]);

  const templateOptions = useMemo(() => {
    return masterData.card_template.map((item) => ({
      label: item.value,
        value: parseInt(item.key, 10),
    }));
  }, [masterData.card_template]);

  // Add maps for fast lookup
  const statusMap = useMemo(() => toLookupMap(statusOptions), [statusOptions]);
  const formatMap = useMemo(() => toLookupMap(formatOptions), [formatOptions]);
  const templateMap = useMemo(() => toLookupMap(templateOptions), [templateOptions]);

  return {
    masterData,
    statusOptions,
    formatOptions,
    templateOptions,
    statusMap,
    formatMap,
    templateMap,
  };
};
