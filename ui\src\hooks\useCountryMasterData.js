import { useState, useEffect } from "react";
import { getCountries } from "../api/global";
import { toast } from "react-toastify";

let countryMasterDataCache = null;

export const useCountryMasterData = () => {
  const [countries, setCountries] = useState([]);

  useEffect(() => {
    const fetchCountries = async () => {
      if (countryMasterDataCache) {
        setCountries(countryMasterDataCache);
        return;
      }
      try {
        const res = await getCountries();
        if (Array.isArray(res.data)) {
          countryMasterDataCache = res.data;
          setCountries(res.data);
        } else {
          throw new Error("Invalid response");
        }
      } catch (error) {
        toast.error("Error fetching country data");
      }
    };

    fetchCountries();
  }, []);

  return { countries };
};
