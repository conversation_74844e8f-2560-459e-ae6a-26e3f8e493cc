import { useState, useEffect, useCallback } from "react";
import { getMasterData } from "../api/global";
import { toast } from "react-toastify";

let documentMasterDataCache = null;

export const useDocumentMasterData = () => {
  const [masterData, setMasterData] = useState(
    documentMasterDataCache || { 
      document_type: [], 
      document_status: [] 
    }
  );

  const fetchMasterData = useCallback(async () => {
    if (documentMasterDataCache) {
      console.log("Using cached document master data:", documentMasterDataCache);
      return;
    }
    
    try {
      console.log("Fetching document master data from API...");
      const res = await getMasterData({
        groups: ["document_type", "document_status"],
      });
      console.log("Document master data API response:", res);
      
      const data = res.data || res;
      documentMasterDataCache = data;
      setMasterData(data);
      console.log("Document master data loaded successfully:", data);
    } catch (error) {
      console.error("Error fetching document master data:", error);
      toast.error("Error fetching document master data");
      
      // Set fallback data if API fails
      const fallbackData = {
        document_type: [
          { key: 1, value: "Passport" },
          { key: 2, value: "Driver's License" },
          { key: 3, value: "National ID" },
          { key: 4, value: "Birth Certificate" },
          { key: 5, value: "Social Security Card" },
          { key: 6, value: "Visa" },
          { key: 7, value: "Other" },
        ],
        document_status: [
          { key: 1, value: "Active" },
          { key: 2, value: "Expired" },
          { key: 3, value: "Pending" },
          { key: 4, value: "Cancelled" },
        ]
      };
      
      documentMasterDataCache = fallbackData;
      setMasterData(fallbackData);
      console.log("Using fallback document master data:", fallbackData);
    }
  }, []);

  useEffect(() => {
    if (!documentMasterDataCache) {
      fetchMasterData();
    }
  }, [fetchMasterData]);

  // Transform data to dropdown format - ensure numeric values
  const documentTypeOptions = (masterData.document_type || []).map((item, index) => {
    const numericValue = parseInt(item.key) || parseInt(item.value) || parseInt(item.id) || (index + 1);
    return {
      label: item.value || item.label || item.name,
      value: numericValue,
    };
  });

  const statusOptions = (masterData.document_status || []).map((item, index) => {
    const numericValue = parseInt(item.key) || parseInt(item.value) || parseInt(item.id) || (index + 1);
    return {
      label: item.value || item.label || item.name,
      value: numericValue,
    };
  });

  return { 
    masterData, 
    documentTypeOptions, 
    statusOptions,
    fetchMasterData 
  };
};
