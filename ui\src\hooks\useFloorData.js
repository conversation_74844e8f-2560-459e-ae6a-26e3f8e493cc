import { useState, useCallback, useEffect } from "react";
import { getFloorsByBuilding } from "../api/global";
import { toast } from "react-toastify";

// Module-level cache for floor data keyed by buildingId
let floorMasterDataCache = {};

export const useFloorData = (buildingId) => {
  const [floors, setFloors] = useState(
    buildingId && floorMasterDataCache[buildingId]
      ? floorMasterDataCache[buildingId]
      : []
  );

  const fetchFloors = useCallback(async () => {
    if (!buildingId) {
      console.log("useFloorData: No buildingId provided");
      return;
    }

    console.log("useFloorData: Fetching floors for buildingId:", buildingId);
    try {
      const response = await getFloorsByBuilding(buildingId);
      console.log("useFloorData: API response:", response);
      const floorArray = response.data?.data || [];
      const fetchedFloors = floorArray.map((f) => ({
        label: f.floor_number,
        value: f.floor_id,
      }));
      console.log("useFloorData: Mapped floors:", fetchedFloors);
      // Optionally update cache:
      floorMasterDataCache[buildingId] = fetchedFloors;
      setFloors(fetchedFloors);
    } catch (error) {
      console.error("useFloorData: Error fetching floors:", error);
      toast.error("Error fetching floors");
    }
  }, [buildingId]);

  useEffect(() => {
    fetchFloors();
  }, [buildingId, fetchFloors]);

  return floors;
};
