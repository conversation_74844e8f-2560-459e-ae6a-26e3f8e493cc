import { useState, useCallback, useEffect, useMemo } from "react";
import { getMasterData } from "../api/global";
import { toast } from "react-toastify";
import { toLookupMap } from "../utils/transformOptions";

let identityDataCache = null;

export const useIdentityData = () => {
  const [masterData, setMasterData] = useState(
    identityDataCache || {
      identity_status: [],
      identity_type: [],
      identity_access_status:[],
    
    }
  );
  const fetchMasterData = useCallback(async () => {
    if (identityDataCache) return; 
    try {
      const res = await getMasterData({
        groups: ["identity_status", "identity_type","identity_access_status"],
      });
      identityDataCache = res.data;
      setMasterData(res.data);
    } catch (error) {
      toast.error("Error fetching building master data");
    }
  }, []);

  useEffect(() => {
    if (!identityDataCache) {
      fetchMasterData();
    }
  }, [fetchMasterData]);

  // Prepare dropdown options using memoization
  const identityStatusOptions = useMemo(() => {
    return masterData.identity_status.map((item) => ({
      label: item.value,
      value: Number(item.key),
    }));
  }, [masterData.identity_status]);

  const typeOptions = useMemo(() => {
    return masterData.identity_type.map((item) => ({
      label: item.value,
      value: Number(item.key),
    }));
  }, [masterData.identity_type]);

  const accessStatusOptions = useMemo(() => {
    return masterData.identity_access_status.map((item) => ({
      label: item.value,
      value: Number(item.key),
    }));
  }, [masterData.identity_access_status]);

     const identityStatusMap = useMemo(() => toLookupMap(identityStatusOptions), [identityStatusOptions]);
  const identityTypeMap = useMemo(() => toLookupMap(typeOptions), [typeOptions]);
  const accessStatusMap = useMemo(() => toLookupMap(accessStatusOptions), [accessStatusOptions]);

  return { masterData, identityStatusOptions, typeOptions ,accessStatusOptions, identityStatusMap, identityTypeMap, accessStatusMap};
};

