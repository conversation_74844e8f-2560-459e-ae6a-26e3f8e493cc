import { useState, useEffect } from "react";
import { getPatientGuestTypes } from "../api/global";
import { toast } from "react-toastify";

export let patientGuestTypeMasterDataCache = null;
export const usePatientGuestTypeMasterData = () => {
  const [relationshipTypes, setRelationshipTypes] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getPatientGuestTypes();
        console.log("API Response:", res); // for debugging
        
        // Make sure we get an array of { key, value } objects
        const raw = res?.data?.patient_guest_relation_type;
        if (Array.isArray(raw)) {
          const types = raw.map((t) => ({
            key:   t.key,   // numeric/string code
            value: t.value, // human‐readable label
          }));
          setRelationshipTypes(types);
          // Cache if you want
          patientGuestTypeMasterDataCache = types;
        }
      } catch (error) {
        console.error("Error fetching guest types:", error);
        toast.error("Failed to load relationship types");
      }
    };
    fetchData();
  }, []);

  return { relationshipTypes };
};
