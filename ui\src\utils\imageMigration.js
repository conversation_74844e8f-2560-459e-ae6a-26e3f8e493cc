/**
 * Image Migration Utility
 * Handles migration from base64 images to file-based system
 */

import imageManager from './imageManager';

/**
 * Migrate template elements from base64 to file-based images
 * @param {Array} elements - Array of template elements
 * @returns {Array} Migrated elements
 */
export const migrateElementImages = (elements) => {
  const migratedElements = elements.map(element => {
    if (element.type === 'image' && element.src) {
      // Check if it's a base64 image
      if (element.src.startsWith('data:image/')) {
        try {
          // Add to image manager
          const tempUrl = imageManager.addImage(
            element.id, 
            element.src, 
            element.imageType || 'static'
          );
          
          return {
            ...element,
            src: tempUrl,
            hasFile: true, // Mark for upload
            originalBase64: element.src // Keep original for fallback
          };
        } catch (error) {
          console.error('Error migrating image for element:', element.id, error);
          return element; // Return original if migration fails
        }
      }
    }
    return element;
  });

  return migratedElements;
};

/**
 * Check if template has base64 images that need migration
 * @param {Array} elements - Array of template elements
 * @returns {boolean} True if migration is needed
 */
export const needsImageMigration = (elements) => {
  return elements.some(element => 
    element.type === 'image' && 
    element.src && 
    element.src.startsWith('data:image/')
  );
};

/**
 * Get statistics about images in template
 * @param {Array} elements - Array of template elements
 * @returns {Object} Statistics object
 */
export const getImageStats = (elements) => {
  const stats = {
    totalImages: 0,
    base64Images: 0,
    urlImages: 0,
    dynamicImages: 0,
    staticImages: 0
  };

  elements.forEach(element => {
    if (element.type === 'image') {
      stats.totalImages++;
      
      if (element.isDynamic) {
        stats.dynamicImages++;
      } else {
        stats.staticImages++;
      }
      
      if (element.src) {
        if (element.src.startsWith('data:image/')) {
          stats.base64Images++;
        } else {
          stats.urlImages++;
        }
      }
    }
  });

  return stats;
};

/**
 * Clean up base64 references after successful migration
 * @param {Array} elements - Array of template elements
 * @returns {Array} Cleaned elements
 */
export const cleanupMigratedElements = (elements) => {
  return elements.map(element => {
    if (element.type === 'image' && element.originalBase64) {
      const { originalBase64, ...cleanElement } = element;
      return cleanElement;
    }
    return element;
  });
};

/**
 * Restore base64 images if migration fails
 * @param {Array} elements - Array of template elements
 * @returns {Array} Restored elements
 */
export const restoreBase64Images = (elements) => {
  return elements.map(element => {
    if (element.type === 'image' && element.originalBase64) {
      return {
        ...element,
        src: element.originalBase64,
        hasFile: false
      };
    }
    return element;
  });
};

/**
 * Validate image URLs and files
 * @param {Array} elements - Array of template elements
 * @returns {Object} Validation results
 */
export const validateImages = async (elements) => {
  const results = {
    valid: [],
    invalid: [],
    pending: []
  };

  for (const element of elements) {
    if (element.type === 'image' && element.src) {
      if (element.hasFile && imageManager.hasPendingUpload(element.id)) {
        results.pending.push(element.id);
      } else if (element.src.startsWith('http') || element.src.startsWith('blob:')) {
        // Validate URL
        try {
          const response = await fetch(element.src, { method: 'HEAD' });
          if (response.ok) {
            results.valid.push(element.id);
          } else {
            results.invalid.push(element.id);
          }
        } catch (error) {
          results.invalid.push(element.id);
        }
      } else if (element.src.startsWith('data:image/')) {
        // Base64 is considered valid
        results.valid.push(element.id);
      } else {
        results.invalid.push(element.id);
      }
    }
  }

  return results;
};

/**
 * Prepare elements for API submission
 * @param {Array} elements - Array of template elements
 * @returns {Object} Prepared data with elements and images
 */
export const prepareElementsForAPI = (elements) => {
  const preparedElements = [];
  const imagesToUpload = [];

  elements.forEach(element => {
    if (element.type === 'image') {
      if (element.hasFile && imageManager.hasPendingUpload(element.id)) {
        // This image needs to be uploaded
        const imageFile = imageManager.getImageFile(element.id);
        if (imageFile) {
          imagesToUpload.push({
            element_id: element.id,
            file: imageFile,
            image_type: element.imageType || 'static'
          });
        }
        
        // Remove file-related properties for API
        const { hasFile, originalBase64, ...apiElement } = element;
        preparedElements.push({
          ...apiElement,
          src: null // Will be set after upload
        });
      } else {
        // Image is already uploaded or is a URL
        const { hasFile, originalBase64, ...apiElement } = element;
        preparedElements.push(apiElement);
      }
    } else {
      preparedElements.push(element);
    }
  });

  return {
    elements: preparedElements,
    images: imagesToUpload
  };
};

/**
 * Update elements with uploaded image URLs
 * @param {Array} elements - Array of template elements
 * @param {Object} uploadResults - Map of element_id -> uploaded URL
 * @returns {Array} Updated elements
 */
export const updateElementsWithUploadedImages = (elements, uploadResults) => {
  return elements.map(element => {
    if (element.type === 'image' && uploadResults[element.id]) {
      return {
        ...element,
        src: uploadResults[element.id],
        hasFile: false
      };
    }
    return element;
  });
};
