import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      light: '#9575CD',
      main: '#4f2683',
      dark: '#4527A0',
      contrastText: '#fff',
    },
    background: {
      paper: '#fff',
      default: '#fff',
    },
    text: {
      primary: '#000',
      paddingRight: '0.5rem',
    },
    grey: {
      300: '#e0e0e0',
      500: '#9e9e9e',  // placeholder
      700: '#616161',
    },
    error: {
      main: '#f44336',
    },
  },
  components: {
    // --- make every TextField outlined + fullWidth by default ---
    MuiTextField: {
      defaultProps: {
        variant: 'outlined',
        fullWidth: true,
      },
    },

    // --- placeholder styling inside any OutlinedInput/TextField ---
    MuiOutlinedInput: {
      styleOverrides: {
        root: ({ theme }) => ({
          marginTop: theme.spacing(2),
          backgroundColor: theme.palette.background.paper,
          height: 40,
          borderRadius: theme.shape.borderRadius,
          boxSizing: 'border-box',
          
          backgroundClip: 'border-box',
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: theme.palette.primary.main,
          },
          '& .MuiInputBase-input': {
            padding: theme.spacing(1, 2),
            // placeholder grey
            '&::placeholder': {
              color: theme.palette.grey[500],
              paddingRight: theme.spacing(16),
              opacity: 1,
            },
          },
           
        }),
        notchedOutline: ({ theme }) => ({
          borderColor: theme.palette.grey[300],
          top: 0,
        }),
      },
    },

    // --- floating label ---
    MuiInputLabel: {
      styleOverrides: {
        root: ({ theme }) => ({
          // Tailwind’s text-sm = 0.875rem
          fontSize: '1.2rem',
          
          // font-medium = 500
          fontWeight: 500,
          // Tailwind gray-700 = #374151
          color: '#374151',
          fontFamily: 'Poppins',
          // mb-1 = theme.spacing(1) = 8px
          marginBottom: theme.spacing(1),
          // remove any top margin
          marginTop: 0,
          textAlign: 'left',                  // ← Ensures text aligns left
          transformOrigin: 'top left', 
          marginBottom: 12,
          paddingLeft: '-15px',  
          marginLeft: '-15px',
        }), 
        // if you don’t want the label to shrink/move on focus,
        // you can either leave focused alone or customize it:
        // focused: {
        //   color: '#374151',
        // },
      },
    },
    // --- helper / error text below inputs ---
    MuiFormHelperText: {
      styleOverrides: {
        root: ({ theme }) => ({
          marginTop: theme.spacing(1),
          color: theme.palette.error.main,
          fontSize: '1.875rem',
        }),
      },
    },

    // --- Clock section (unchanged) ---
    MuiMultiSectionDigitalClockSection: {
      styleOverrides: {
        itemButtonSelected: ({ theme }) => ({
          backgroundColor: theme.palette.primary.main,
          color: theme.palette.primary.contrastText,
          borderRadius: theme.shape.borderRadius,
          '&:hover': {
            backgroundColor: theme.palette.primary.dark,
          },
        }),
      },
    },

    // --- react-datepicker overrides (unchanged) ---
    MuiCssBaseline: {
      styleOverrides: (themeParam) => ({
        '.react-datepicker__input-container input': {
          backgroundColor: theme.palette.background.paper,
          marginTop: themeParam.spacing(3),
          height: 40,
          padding: themeParam.spacing(1, 2),
          borderRadius: themeParam.shape.borderRadius,
          border: `1px solid ${themeParam.palette.grey[300]}`,
          boxSizing: 'border-box',
          '&:hover': {
            borderColor: themeParam.palette.primary.main,
          },
          '&:focus': {
            outline: 'none',
            borderColor: themeParam.palette.primary.main,
            boxShadow: `0 0 0 2px ${themeParam.palette.primary.light}`,
          },
          '&::placeholder': {
            color: themeParam.palette.grey[500],
            opacity: 1,
          },
        },
        /* calendar popover styles... */
        '.react-datepicker': { /* ... */ },
        '.react-datepicker__header': { /* ... */ },
        '.react-datepicker__day--selected, .react-datepicker__day--keyboard-selected': { /* ... */ },
        '.react-datepicker__day:hover': { /* ... */ },
        '.react-datepicker__year-dropdown, .react-datepicker__month-dropdown': { /* ... */ },
      }),
    },
  },
});

export default theme;
